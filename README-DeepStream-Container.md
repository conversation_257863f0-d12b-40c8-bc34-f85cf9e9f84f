# DeepStream 7.1 OCI Container with CUDA 12.6

This repository contains Yocto recipes to build an OCI container image with NVIDIA DeepStream 7.1, CUDA 12.6, and compatible GStreamer for AI video processing applications.

## Overview

The container includes:
- **NVIDIA DeepStream SDK 7.1** - Complete streaming analytics toolkit
- **CUDA Toolkit 12.6** - GPU computing platform and runtime
- **GStreamer 1.20.3** - Multimedia framework with DeepStream plugins
- **Python 3** - With DeepStream Python bindings
- **OpenCV** - Computer vision library
- **Development tools** - For building custom applications

## Prerequisites

### Hardware Requirements
- NVIDIA GPU with compute capability 6.0 or higher
- Minimum 8GB GPU memory recommended
- x86_64 or aarch64 architecture

### Software Requirements
- NVIDIA driver version 560.28.03 or later
- Docker or Podman with NVIDIA container runtime
- Skopeo (for OCI image conversion)

## Building the Container

### 1. Build with Yocto/Bitbake

```bash
# Build the DeepStream container image
bitbake deepstream-container-image

# The OCI image will be created in:
# tmp/deploy/images/<machine>/deepstream-container-image-<machine>-<timestamp>.rootfs-oci
```

### 2. Convert to Docker Format with Skopeo

```bash
# Extract the OCI image (if it's a tarball)
cd tmp/deploy/images/<machine>/
tar -xf deepstream-container-image-*.rootfs-oci*.tar

# Convert OCI to Docker format and push to registry
skopeo copy \
    oci:deepstream-container-image-<machine>-<timestamp>.rootfs-oci:latest \
    docker://your-registry.com/deepstream:7.1-cuda12.6

# Or save as Docker archive
skopeo copy \
    oci:deepstream-container-image-<machine>-<timestamp>.rootfs-oci:latest \
    docker-archive:deepstream-7.1-cuda12.6.tar:deepstream:latest
```

### 3. Load into Docker

```bash
# Load from archive
docker load < deepstream-7.1-cuda12.6.tar

# Or pull from registry
docker pull your-registry.com/deepstream:7.1-cuda12.6
```

## Running the Container

### Basic Usage

```bash
# Run with NVIDIA runtime
docker run --runtime=nvidia --gpus all -it deepstream:latest

# Run with specific GPU
docker run --runtime=nvidia --gpus '"device=0"' -it deepstream:latest

# Run with volume mounts for data
docker run --runtime=nvidia --gpus all \
    -v /path/to/models:/models \
    -v /path/to/data:/data \
    -v /path/to/output:/output \
    -it deepstream:latest
```

### Testing the Installation

```bash
# Inside the container, test DeepStream installation
gst-deepstream-test.py --check

# List available pipeline templates
gst-deepstream-test.py --list-pipelines

# Run a basic test pipeline
gst-deepstream-test.py --run-sample test_basic
```

### Running DeepStream Applications

```bash
# Run the reference DeepStream application
deepstream-app -c /opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/source1_usb_dec_infer_resnet_int8.txt

# Run a custom GStreamer pipeline
gst-launch-1.0 videotestsrc num-buffers=300 ! \
    nvvideoconvert ! \
    'video/x-raw(memory:NVMM),format=NV12' ! \
    nvstreammux name=mux batch-size=1 width=1920 height=1080 ! \
    nvinfer config-file-path=/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt ! \
    nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink
```

## Container Structure

```
/
├── opt/nvidia/deepstream/deepstream-7.1/    # DeepStream SDK
├── usr/local/cuda-12.6/                     # CUDA Toolkit
├── usr/lib/gstreamer-1.0/                   # GStreamer plugins
├── workspace/                               # Working directory
├── models/                                  # Model storage
├── data/                                    # Input data
└── output/                                  # Output results
```

## Environment Variables

The container sets the following environment variables:

```bash
CUDA_VERSION=12.6
DEEPSTREAM_VERSION=7.1
NVIDIA_VISIBLE_DEVICES=all
NVIDIA_DRIVER_CAPABILITIES=compute,video,utility
GST_PLUGIN_PATH=/opt/nvidia/deepstream/deepstream-7.1/lib/gst-plugins:/usr/lib/gstreamer-1.0
LD_LIBRARY_PATH=/opt/nvidia/deepstream/deepstream-7.1/lib:/usr/local/cuda-12.6/lib64:/usr/lib
PATH=/opt/nvidia/deepstream/deepstream-7.1/bin:/usr/local/cuda-12.6/bin:$PATH
```

## Sample Pipelines

### RTSP Stream Processing

```bash
gst-launch-1.0 rtspsrc location=rtsp://your-camera-ip:554/stream latency=0 ! \
    rtph264depay ! h264parse ! nvv4l2decoder ! \
    nvvideoconvert ! 'video/x-raw(memory:NVMM),format=NV12' ! \
    nvstreammux name=mux batch-size=1 width=1920 height=1080 ! \
    nvinfer config-file-path=/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt ! \
    nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink
```

### File Processing with Output

```bash
gst-launch-1.0 filesrc location=/data/input.mp4 ! \
    decodebin ! nvvideoconvert ! \
    'video/x-raw(memory:NVMM),format=NV12' ! \
    nvstreammux name=mux batch-size=1 width=1920 height=1080 ! \
    nvinfer config-file-path=/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt ! \
    nvvideoconvert ! nvdsosd ! nvvideoconvert ! \
    'video/x-raw,format=I420' ! x264enc ! mp4mux ! \
    filesink location=/output/result.mp4
```

## Python Development

The container includes Python bindings for DeepStream:

```python
import pyds
import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst

# Initialize GStreamer
Gst.init(None)

# Your DeepStream Python application code here
```

## Troubleshooting

### Common Issues

1. **NVIDIA runtime not found**
   ```bash
   # Install nvidia-container-runtime
   sudo apt-get install nvidia-container-runtime
   ```

2. **GPU not accessible**
   ```bash
   # Check NVIDIA driver
   nvidia-smi
   
   # Verify container runtime
   docker run --runtime=nvidia --rm nvidia/cuda:12.6-base-ubuntu20.04 nvidia-smi
   ```

3. **GStreamer plugins not found**
   ```bash
   # Inside container, check plugin path
   echo $GST_PLUGIN_PATH
   gst-inspect-1.0 nvvideoconvert
   ```

### Debugging

```bash
# Enable GStreamer debug output
export GST_DEBUG=3
export GST_DEBUG_DUMP_DOT_DIR=/tmp/gst-debug

# Run with debug information
gst-deepstream-test.py --run-sample test_basic
```

## License

This container includes proprietary NVIDIA software components:
- NVIDIA CUDA Toolkit (NVIDIA CUDA License)
- NVIDIA DeepStream SDK (NVIDIA DeepStream License)

Please review the respective license agreements before use.

## Support

For issues related to:
- **DeepStream SDK**: [NVIDIA Developer Forums](https://forums.developer.nvidia.com/c/accelerated-computing/intelligent-video-analytics/deepstream-sdk/)
- **CUDA**: [NVIDIA CUDA Documentation](https://docs.nvidia.com/cuda/)
- **Container build**: Check the Yocto build logs and recipe configurations
