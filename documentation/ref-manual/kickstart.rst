.. SPDX-License-Identifier: CC-BY-SA-2.0-UK

*******************************************
OpenEmbedded Kickstart (``.wks``) Reference
*******************************************

.. _openembedded-kickstart-wks-reference:

Introduction
============

The current Wic implementation supports only the basic kickstart
partitioning commands: ``partition`` (or ``part`` for short) and
``bootloader``.

.. note::

   Future updates will implement more commands and options. If you use
   anything that is not specifically supported, results can be
   unpredictable.

This chapter provides a reference on the available kickstart commands.
The information lists the commands, their syntax, and meanings.
Kickstart commands are based on the Fedora kickstart versions but with
modifications to reflect Wic capabilities. You can see the original
documentation for those commands at the following link:
https://pykickstart.readthedocs.io/en/latest/kickstart-docs.html

Command: part or partition
==========================

Either of these commands creates a partition on the system and uses the
following syntax::

   part [mntpoint]
   partition [mntpoint]

If you do not
provide mntpoint, Wic creates a partition but does not mount it.

The ``mntpoint`` is where the partition is mounted and must be in one of
the following forms:

-  ``/path``: For example, "/", "/usr", or "/home"

-  ``swap``: The created partition is used as swap space

Specifying a mntpoint causes the partition to automatically be mounted.
Wic achieves this by adding entries to the filesystem table (fstab)
during image generation. In order for Wic to generate a valid fstab, you
must also provide one of the ``--ondrive``, ``--ondisk``, or
``--use-uuid`` partition options as part of the command.

.. note::

   The mount program must understand the PARTUUID syntax you use with
   ``--use-uuid`` and non-root *mountpoint*, including swap. The default
   configuration of BusyBox in OpenEmbedded supports this, but this may
   be disabled in custom configurations.

Here is an example that uses "/" as the mountpoint. The command uses
``--ondisk`` to force the partition onto the ``sdb`` disk::

      part / --source rootfs --ondisk sdb --fstype=ext3 --label platform --align 1024

Here is a list that describes other supported options you can use with
the ``part`` and ``partition`` commands:

-  ``--size``: The minimum partition size. Specify as an integer value
   optionally followed by one of the units "k" / "K" for kibibyte,
   "M" for mebibyte and "G" for gibibyte. The default unit if none is
   given is "M". You do not need this option if you use ``--source``.

-  ``--fixed-size``: The exact partition size. Specify as an integer
   value optionally followed by one of the units "k" / "K" for kibibyte,
   "M" for mebibyte and "G" for gibibyte. The default unit if none is
   given is "M".  Cannot be specify together with ``--size``. An error
   occurs when assembling the disk image if the partition data is larger
   than ``--fixed-size``.

-  ``--source``: This option is a Wic-specific option that names the
   source of the data that populates the partition. The most common
   value for this option is "rootfs", but you can use any value that
   maps to a valid source plugin. For information on the source plugins,
   see the ":ref:`dev-manual/common-tasks:using the wic plugin interface`"
   section in the Yocto Project Development Tasks Manual.

   If you use ``--source rootfs``, Wic creates a partition as large as
   needed and fills it with the contents of the root filesystem pointed
   to by the ``-r`` command-line option or the equivalent root filesystem derived
   from the ``-e`` command-line option. The filesystem type used to
   create the partition is driven by the value of the ``--fstype``
   option specified for the partition. See the entry on ``--fstype``
   that follows for more information.

   If you use ``--source plugin-name``, Wic creates a partition as large
   as needed and fills it with the contents of the partition that is
   generated by the specified plugin name using the data pointed to by
   the ``-r`` command-line option or the equivalent root filesystem derived from
   the ``-e`` command-line option. Exactly what those contents are and
   filesystem type used are dependent on the given plugin
   implementation.

   If you do not use the ``--source`` option, the ``wic`` command
   creates an empty partition. Consequently, you must use the ``--size``
   option to specify the size of the empty partition.

-  ``--ondisk`` or ``--ondrive``: Forces the partition to be created
   on a particular disk.

-  ``--fstype``: Sets the file system type for the partition. Valid
   values are:

   -  ``btrfs``

   -  ``erofs``

   -  ``ext2``

   -  ``ext3``

   -  ``ext4``

   -  ``squashfs``

   -  ``swap``

   -  ``vfat``

-  ``--fsoptions``: Specifies a free-form string of options to be used
   when mounting the filesystem. This string is copied into the
   ``/etc/fstab`` file of the installed system and should be enclosed in
   quotes. If not specified, the default string is "defaults".

-  ``--label label``: Specifies the label to give to the filesystem to
   be made on the partition. If the given label is already in use by
   another filesystem, a new label is created for the partition.

-  ``--active``: Marks the partition as active.

-  ``--align (in KBytes)``: This option is a Wic-specific option that
   says to start partitions on boundaries given x KBytes.

-  ``--offset``: This option is a Wic-specific option that
   says to place a partition at exactly the specified offset. If the
   partition cannot be placed at the specified offset, the image build
   will fail. Specify as an integer value optionally followed by one of
   the units "s" / "S" for 512 byte sector, "k" / "K" for kibibyte, "M"
   for mebibyte and "G" for gibibyte. The default unit if none is given
   is "k".

-  ``--no-table``: This option is a Wic-specific option. Using the
   option reserves space for the partition and causes it to become
   populated. However, the partition is not added to the partition
   table.

-  ``--exclude-path``: This option is a Wic-specific option that
   excludes the given relative path from the resulting image. This
   option is only effective with the rootfs source plugin.

-  ``--extra-space``: This option is a Wic-specific option that adds
   extra space after the space filled by the content of the partition.
   The final size can exceed the size specified by the ``--size``
   option. The default value is 10M. Specify as an integer value
   optionally followed by one of the units "k" / "K" for kibibyte, "M"
   for mebibyte and "G" for gibibyte. The default unit if none is given
   is "M".

-  ``--overhead-factor``: This option is a Wic-specific option that
   multiplies the size of the partition by the option's value. You must
   supply a value greater than or equal to "1". The default value is
   "1.3".

-  ``--part-name``: This option is a Wic-specific option that
   specifies a name for GPT partitions.

-  ``--part-type``: This option is a Wic-specific option that
   specifies the partition type globally unique identifier (GUID) for
   GPT partitions. You can find the list of partition type GUIDs at
   https://en.wikipedia.org/wiki/GUID_Partition_Table#Partition_type_GUIDs.

-  ``--use-uuid``: This option is a Wic-specific option that causes
   Wic to generate a random GUID for the partition. The generated
   identifier is used in the bootloader configuration to specify the
   root partition.

-  ``--uuid``: This option is a Wic-specific option that specifies the
   partition UUID.

-  ``--fsuuid``: This option is a Wic-specific option that specifies
   the filesystem UUID. You can generate or modify
   :term:`WKS_FILE` with this option if a preconfigured
   filesystem UUID is added to the kernel command line in the bootloader
   configuration before you run Wic.

-  ``--system-id``: This option is a Wic-specific option that
   specifies the partition system ID, which is a one byte long,
   hexadecimal parameter with or without the 0x prefix.

-  ``--mkfs-extraopts``: This option specifies additional options to
   pass to the ``mkfs`` utility. Some default options for certain
   filesystems do not take effect. See Wic's help on kickstart (i.e.
   ``wic help kickstart``).

Command: bootloader
===================

This command specifies how the bootloader should be configured and
supports the following options:

.. note::

   Bootloader functionality and boot partitions are implemented by the
   various
   --source
   plugins that implement bootloader functionality. The bootloader
   command essentially provides a means of modifying bootloader
   configuration.

-  ``--timeout``: Specifies the number of seconds before the
   bootloader times out and boots the default option.

-  ``--append``: Specifies kernel parameters. These parameters will be
   added to the syslinux :term:`APPEND` or ``grub`` kernel command line.

-  ``--configfile``: Specifies a user-defined configuration file for
   the bootloader. You can provide a full pathname for the file or a
   file located in the ``canned-wks`` folder. This option overrides
   all other bootloader options.
