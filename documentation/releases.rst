.. SPDX-License-Identifier: CC-BY-SA-2.0-UK

..
   NOTE FOR RELEASE MAINTAINERS:
   This file only needs updating in the development release ("master" branch)
   When documentation for stable releases is built,
   the latest version from "master" is used
   by https://git.yoctoproject.org/yocto-autobuilder-helper/tree/scripts/run-docs-build

===========================
 Supported Release Manuals
===========================

******************************
Release Series 4.0 (kirkstone)
******************************

- :yocto_docs:`4.0 Documentation </4.0>`

******************************
Release Series 3.4 (honister)
******************************

- :yocto_docs:`3.4 Documentation </3.4>`
- :yocto_docs:`3.4.1 Documentation </3.4.1>`
- :yocto_docs:`3.4.2 Documentation </3.4.2>`
- :yocto_docs:`3.4.3 Documentation </3.4.3>`

******************************
Release Series 3.3 (hardknott)
******************************

- :yocto_docs:`3.3 Documentation </3.3>`
- :yocto_docs:`3.3.1 Documentation </3.3.1>`
- :yocto_docs:`3.3.2 Documentation </3.3.2>`
- :yocto_docs:`3.3.3 Documentation </3.3.3>`
- :yocto_docs:`3.3.4 Documentation </3.3.4>`
- :yocto_docs:`3.3.5 Documentation </3.3.5>`

****************************
Release Series 3.1 (dunfell)
****************************

- :yocto_docs:`3.1 Documentation </3.1>`
- :yocto_docs:`3.1.1 Documentation </3.1.1>`
- :yocto_docs:`3.1.2 Documentation </3.1.2>`
- :yocto_docs:`3.1.3 Documentation </3.1.3>`
- :yocto_docs:`3.1.4 Documentation </3.1.4>`
- :yocto_docs:`3.1.5 Documentation </3.1.5>`
- :yocto_docs:`3.1.6 Documentation </3.1.6>`
- :yocto_docs:`3.1.7 Documentation </3.1.7>`
- :yocto_docs:`3.1.8 Documentation </3.1.8>`
- :yocto_docs:`3.1.9 Documentation </3.1.9>`
- :yocto_docs:`3.1.10 Documentation </3.1.10>`
- :yocto_docs:`3.1.11 Documentation </3.1.11>`
- :yocto_docs:`3.1.12 Documentation </3.1.12>`
- :yocto_docs:`3.1.13 Documentation </3.1.13>`
- :yocto_docs:`3.1.14 Documentation </3.1.14>`
- :yocto_docs:`3.1.15 Documentation </3.1.15>`

==========================
 Outdated Release Manuals
==========================

*******************************
Release Series 3.2 (gatesgarth)
*******************************

- :yocto_docs:`3.2 Documentation </3.2>`
- :yocto_docs:`3.2.1 Documentation </3.2.1>`
- :yocto_docs:`3.2.2 Documentation </3.2.2>`
- :yocto_docs:`3.2.3 Documentation </3.2.3>`
- :yocto_docs:`3.2.4 Documentation </3.2.4>`

*************************
Release Series 3.0 (zeus)
*************************

- :yocto_docs:`3.0 Documentation </3.0>`
- :yocto_docs:`3.0.1 Documentation </3.0.1>`
- :yocto_docs:`3.0.2 Documentation </3.0.2>`
- :yocto_docs:`3.0.3 Documentation </3.0.3>`
- :yocto_docs:`3.0.4 Documentation </3.0.4>`

****************************
Release Series 2.7 (warrior)
****************************

- :yocto_docs:`2.7 Documentation </2.7>`
- :yocto_docs:`2.7.1 Documentation </2.7.1>`
- :yocto_docs:`2.7.2 Documentation </2.7.2>`
- :yocto_docs:`2.7.3 Documentation </2.7.3>`
- :yocto_docs:`2.7.4 Documentation </2.7.4>`

*************************
Release Series 2.6 (thud)
*************************

- :yocto_docs:`2.6 Documentation </2.6>`
- :yocto_docs:`2.6.1 Documentation </2.6.1>`
- :yocto_docs:`2.6.2 Documentation </2.6.2>`
- :yocto_docs:`2.6.3 Documentation </2.6.3>`
- :yocto_docs:`2.6.4 Documentation </2.6.4>`

*************************
Release Series 2.5 (sumo)
*************************

- :yocto_docs:`2.5 Documentation </2.5>`
- :yocto_docs:`2.5.1 Documentation </2.5.1>`
- :yocto_docs:`2.5.2 Documentation </2.5.2>`
- :yocto_docs:`2.5.3 Documentation </2.5.3>`

**************************
Release Series 2.4 (rocko)
**************************

- :yocto_docs:`2.4 Documentation </2.4>`
- :yocto_docs:`2.4.1 Documentation </2.4.1>`
- :yocto_docs:`2.4.2 Documentation </2.4.2>`
- :yocto_docs:`2.4.3 Documentation </2.4.3>`
- :yocto_docs:`2.4.4 Documentation </2.4.4>`

*************************
Release Series 2.3 (pyro)
*************************

- :yocto_docs:`2.3 Documentation </2.3>`
- :yocto_docs:`2.3.1 Documentation </2.3.1>`
- :yocto_docs:`2.3.2 Documentation </2.3.2>`
- :yocto_docs:`2.3.3 Documentation </2.3.3>`
- :yocto_docs:`2.3.4 Documentation </2.3.4>`

**************************
Release Series 2.2 (morty)
**************************

- :yocto_docs:`2.2 Documentation </2.2>`
- :yocto_docs:`2.2.1 Documentation </2.2.1>`
- :yocto_docs:`2.2.2 Documentation </2.2.2>`
- :yocto_docs:`2.2.3 Documentation </2.2.3>`

****************************
Release Series 2.1 (krogoth)
****************************

- :yocto_docs:`2.1 Documentation </2.1>`
- :yocto_docs:`2.1.1 Documentation </2.1.1>`
- :yocto_docs:`2.1.2 Documentation </2.1.2>`
- :yocto_docs:`2.1.3 Documentation </2.1.3>`

***************************
Release Series 2.0 (jethro)
***************************

- :yocto_docs:`1.9 Documentation </1.9>`
- :yocto_docs:`2.0 Documentation </2.0>`
- :yocto_docs:`2.0.1 Documentation </2.0.1>`
- :yocto_docs:`2.0.2 Documentation </2.0.2>`
- :yocto_docs:`2.0.3 Documentation </2.0.3>`

*************************
Release Series 1.8 (fido)
*************************

- :yocto_docs:`1.8 Documentation </1.8>`
- :yocto_docs:`1.8.1 Documentation </1.8.1>`
- :yocto_docs:`1.8.2 Documentation </1.8.2>`

**************************
Release Series 1.7 (dizzy)
**************************

- :yocto_docs:`1.7 Documentation </1.7>`
- :yocto_docs:`1.7.1 Documentation </1.7.1>`
- :yocto_docs:`1.7.2 Documentation </1.7.2>`
- :yocto_docs:`1.7.3 Documentation </1.7.3>`

**************************
Release Series 1.6 (daisy)
**************************

- :yocto_docs:`1.6 Documentation </1.6>`
- :yocto_docs:`1.6.1 Documentation </1.6.1>`
- :yocto_docs:`1.6.2 Documentation </1.6.2>`
- :yocto_docs:`1.6.3 Documentation </1.6.3>`

*************************
Release Series 1.5 (dora)
*************************

- :yocto_docs:`1.5 Documentation </1.5>`
- :yocto_docs:`1.5.1 Documentation </1.5.1>`
- :yocto_docs:`1.5.2 Documentation </1.5.2>`
- :yocto_docs:`1.5.3 Documentation </1.5.3>`
- :yocto_docs:`1.5.4 Documentation </1.5.4>`

**************************
Release Series 1.4 (dylan)
**************************

- :yocto_docs:`1.4 Documentation </1.4>`
- :yocto_docs:`1.4.1 Documentation </1.4.1>`
- :yocto_docs:`1.4.2 Documentation </1.4.2>`
- :yocto_docs:`1.4.3 Documentation </1.4.3>`
- :yocto_docs:`1.4.4 Documentation </1.4.4>`
- :yocto_docs:`1.4.5 Documentation </1.4.5>`

**************************
Release Series 1.3 (danny)
**************************

- :yocto_docs:`1.3 Documentation </1.3>`
- :yocto_docs:`1.3.1 Documentation </1.3.1>`
- :yocto_docs:`1.3.2 Documentation </1.3.2>`

***************************
Release Series 1.2 (denzil)
***************************

- :yocto_docs:`1.2 Documentation </1.2>`
- :yocto_docs:`1.2.1 Documentation </1.2.1>`
- :yocto_docs:`1.2.2 Documentation </1.2.2>`

***************************
Release Series 1.1 (edison)
***************************

- :yocto_docs:`1.1 Documentation </1.1>`
- :yocto_docs:`1.1.1 Documentation </1.1.1>`
- :yocto_docs:`1.1.2 Documentation </1.1.2>`

****************************
Release Series 1.0 (bernard)
****************************

- :yocto_docs:`1.0 Documentation </1.0>`
- :yocto_docs:`1.0.1 Documentation </1.0.1>`
- :yocto_docs:`1.0.2 Documentation </1.0.2>`

****************************
Release Series 0.9 (laverne)
****************************

- :yocto_docs:`0.9 Documentation </0.9>`
