.. SPDX-License-Identifier: CC-BY-SA-2.0-UK

.. The Yocto Project documentation master file, created by
   sphinx-quickstart on Mon Apr 13 09:38:33 2020.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to the Yocto Project Documentation
==========================================

|

.. toctree::
   :maxdepth: 1
   :caption: Introduction and Overview

   Quick Build <brief-yoctoprojectqs/index>
   what-i-wish-id-known
   transitioning-to-a-custom-environment
   Yocto Project Software Overview <https://www.yoctoproject.org/software-overview/>
   Tips and Tricks Wiki <https://wiki.yoctoproject.org/wiki/TipsAndTricks>


.. toctree::
   :maxdepth: 1
   :caption: Manuals

   Overview and Concepts Manual <overview-manual/index>
   Reference Manual <ref-manual/index>
   Board Support Package (BSP) Developer's guide <bsp-guide/index>
   Development Tasks Manual <dev-manual/index>
   Linux Kernel Development Manual <kernel-dev/index>
   Profile and Tracing Manual <profile-manual/index>
   Application Development and the Extensible SDK (eSDK) <sdk-manual/index>
   Toaster Manual <toaster-manual/index>
   Test Environment Manual <test-manual/index>
   bitbake

.. toctree::
   :maxdepth: 1
   :caption: Release Manuals
   :hidden:

   Release Information <migration-guides/index>
   releases

.. toctree::
   :maxdepth: 1
   :caption: Documentation Index
   :hidden:

   genindex
