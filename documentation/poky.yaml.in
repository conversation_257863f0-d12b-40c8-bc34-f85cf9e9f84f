DISTRO : "4.0"
DISTRO_NAME_NO_CAP : "kirkstone"
DISTRO_NAME : "Kirkstone"
DISTRO_NAME_NO_CAP_MINUS_ONE : "honister"
DISTRO_NAME_NO_CAP_LTS : "dunfell"
YOCTO_DOC_VERSION : "4.0"
DISTRO_REL_TAG : "yocto-4.0"
DOCCONF_VERSION : "dev"
BITBAKE_SERIES : ""
YOCTO_DL_URL : "https://downloads.yoctoproject.org"
YOCTO_AB_URL : "https://autobuilder.yoctoproject.org"
YOCTO_RELEASE_DL_URL : "&YOCTO_DL_URL;/releases/yocto/yocto-&DISTRO;"
UBUNTU_HOST_PACKAGES_ESSENTIAL : "gawk wget git diffstat unzip texinfo gcc \
     build-essential chrpath socat cpio python3 python3-pip python3-pexpect \
     xz-utils debianutils iputils-ping python3-git python3-jinja2 libegl1-mesa libsdl1.2-dev \
     pylint3 xterm python3-subunit mesa-common-dev zstd liblz4-tool"
FEDORA_HOST_PACKAGES_ESSENTIAL : "gawk make wget tar bzip2 gzip python3 unzip perl patch \
     diffutils diffstat git cpp gcc gcc-c++ glibc-devel texinfo chrpath \
     ccache perl-Data-Dumper perl-Text-ParseWords perl-Thread-Queue perl-bignum socat \
     python3-pexpect findutils which file cpio python python3-pip xz python3-GitPython \
     python3-jinja2 SDL-devel xterm rpcgen mesa-libGL-devel perl-FindBin perl-File-Compare \
     perl-File-Copy perl-locale zstd lz4"
OPENSUSE_HOST_PACKAGES_ESSENTIAL : "python gcc gcc-c++ git chrpath make wget python-xml \
     diffstat makeinfo python-curses patch socat python3 python3-curses tar python3-pip \
     python3-pexpect xz which python3-Jinja2 Mesa-libEGL1 libSDL-devel xterm rpcgen Mesa-dri-devel \
     zstd lz4
     \n\      $ sudo pip3 install GitPython"
CENTOS7_HOST_PACKAGES_ESSENTIAL : "-y epel-release
     \n\      $ sudo yum makecache
     \n\      $ sudo yum install gawk make wget tar bzip2 gzip python3 unzip perl patch \
     diffutils diffstat git cpp gcc gcc-c++ glibc-devel texinfo chrpath socat \
     perl-Data-Dumper perl-Text-ParseWords perl-Thread-Queue python3-pip xz \
     which SDL-devel xterm mesa-libGL-devel zstd lz4
     \n\      $ sudo pip3 install GitPython jinja2"
CENTOS8_HOST_PACKAGES_ESSENTIAL : "-y epel-release
     \n\      $ sudo dnf config-manager --set-enabled PowerTools
     \n\      $ sudo dnf makecache
     \n\      $ sudo dnf install gawk make wget tar bzip2 gzip python3 unzip perl patch \
     diffutils diffstat git cpp gcc gcc-c++ glibc-devel texinfo chrpath ccache \
     socat perl-Data-Dumper perl-Text-ParseWords perl-Thread-Queue python3-pip \
     python3-GitPython python3-jinja2 python3-pexpect xz which SDL-devel xterm \
     rpcgen mesa-libGL-devel zstd lz4"
PIP3_HOST_PACKAGES_DOC : "$ sudo pip3 install sphinx sphinx_rtd_theme pyyaml"
MIN_PYTHON_VERSION : "3.6.0"
MIN_TAR_VERSION : "1.28"
MIN_GIT_VERSION : "*******"
MIN_GCC_VERSION : "5.0"
