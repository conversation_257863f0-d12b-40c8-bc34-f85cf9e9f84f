.. SPDX-License-Identifier: CC-BY-SA-2.0-UK

******************************************
The Yocto Project Development Tasks Manual
******************************************

Welcome
=======

Welcome to the Yocto Project Development Tasks Manual. This manual
provides relevant procedures necessary for developing in the Yocto
Project environment (i.e. developing embedded Linux images and
user-space applications that run on targeted devices). This manual groups
related procedures into higher-level sections. Procedures can consist of
high-level steps or low-level steps depending on the topic.

This manual provides the following:

-  Procedures that help you get going with the Yocto Project; for
   example, procedures that show you how to set up a build host and work
   with the Yocto Project source repositories.

-  Procedures that show you how to submit changes to the Yocto Project.
   Changes can be improvements, new features, or bug fixes.

-  Procedures related to "everyday" tasks you perform while developing
   images and applications using the Yocto Project, such as
   creating a new layer, customizing an image, writing a new recipe,
   and so forth.

This manual does not provide the following:

-  Redundant step-by-step instructions: For example, the
   :doc:`/sdk-manual/index` manual contains detailed
   instructions on how to install an SDK, which is used to develop
   applications for target hardware.

-  Reference or conceptual material: This type of material resides in an
   appropriate reference manual. As an example, system variables are
   documented in the :doc:`/ref-manual/index`.

-  Detailed public information not specific to the Yocto Project: For
   example, exhaustive information on how to use the Git version
   control system is better covered with Internet searches and official Git
   documentation than through the Yocto Project documentation.

Other Information
=================

Because this manual presents information for many different topics,
supplemental information is recommended for full comprehension. For
introductory information on the Yocto Project, see the
:yocto_home:`Yocto Project Website <>`. If you want to build an image with no
knowledge of Yocto Project as a way of quickly testing it out, see the
:doc:`/brief-yoctoprojectqs/index` document.

For a comprehensive list of links and other documentation, see the
":ref:`ref-manual/resources:links and related documentation`"
section in the Yocto Project Reference Manual.
