.. SPDX-License-Identifier: CC-BY-SA-2.0-UK

**********************************************
The Yocto Project Overview and Concepts Manual
**********************************************

Welcome
=======

Welcome to the Yocto Project Overview and Concepts Manual! This manual
introduces the Yocto Project by providing concepts, software overviews,
best-known-methods (BKMs), and any other high-level introductory
information suitable for a new Yocto Project user.

Here is what you can get from this manual:

-  :ref:`overview-manual/yp-intro:introducing the yocto project`\ *:*
   This chapter provides an introduction to the Yocto Project. You will learn
   about features and challenges of the Yocto Project, the layer model,
   components and tools, development methods, the
   :term:`Poky` reference distribution, the
   OpenEmbedded build system workflow, and some basic Yocto terms.

-  :ref:`overview-manual/development-environment:the yocto project development environment`\ *:*
   This chapter helps you get started understanding the Yocto Project
   development environment. You will learn about open source, development hosts,
   Yocto Project source repositories, workflows using Git and the Yocto
   Project, a Git primer, and information about licensing.

-  :doc:`/overview-manual/concepts` *:* This
   chapter presents various concepts regarding the Yocto Project. You
   can find conceptual information about components, development,
   cross-toolchains, and so forth.

This manual does not give you the following:

-  *Step-by-step Instructions for Development Tasks:* Instructional
   procedures reside in other manuals within the Yocto Project
   documentation set. For example, the :doc:`/dev-manual/index`
   provides examples on how to perform
   various development tasks. As another example, the 
   :doc:`/sdk-manual/index` manual contains detailed
   instructions on how to install an SDK, which is used to develop
   applications for target hardware.

-  *Reference Material:* This type of material resides in an appropriate
   reference manual. For example, system variables are documented in the
   :doc:`/ref-manual/index`. As another
   example, the :doc:`/bsp-guide/index` contains reference information on
   BSPs.

-  *Detailed Public Information Not Specific to the Yocto Project:* For
   example, exhaustive information on how to use the Source Control
   Manager Git is better covered with Internet searches and official Git
   Documentation than through the Yocto Project documentation.

Other Information
=================

Because this manual presents information for many different topics,
supplemental information is recommended for full comprehension. For
additional introductory information on the Yocto Project, see the
:yocto_home:`Yocto Project Website <>`. If you want to build an image
with no knowledge of Yocto Project as a way of quickly testing it out,
see the :doc:`/brief-yoctoprojectqs/index` document.
For a comprehensive list of links and other documentation, see the
":ref:`Links and Related
Documentation <resources-links-and-related-documentation>`"
section in the Yocto Project Reference Manual.
