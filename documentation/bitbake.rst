.. SPDX-License-Identifier: CC-BY-SA-2.0-UK

=====================
BitBake Documentation
=====================

|

BitBake was originally a part of the OpenEmbedded project. It was inspired by
the Portage package management system used by the Gentoo Linux distribution. In
2004, the OpenEmbedded project was split the project into two distinct pieces:

-  BitBake, a generic task executor
-  OpenEmbedded, a metadata set utilized by BitBake

Today, BitBake is the primary build tool of OpenEmbedded based projects, such as
the Yocto Project.

The BitBake documentation can be found :doc:`here <bitbake:index>`.
