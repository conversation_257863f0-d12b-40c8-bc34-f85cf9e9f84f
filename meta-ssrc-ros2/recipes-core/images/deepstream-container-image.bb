SUMMARY = "DeepStream 7.1 OCI container image with CUDA 12.6 and G<PERSON><PERSON><PERSON><PERSON>"
DESCRIPTION = "Complete OCI container image with NVIDIA DeepStream 7.1, CUDA 12.6, and compatible GStreamer for AI video processing applications"
LICENSE = "MIT"
LIC_FILES_CHKSUM = "file://${COREBASE}/meta/COPYING.MIT;md5=3da9cfbcb788c80a0384361b4de20420"

require recipes-extended/images/container-base.bb

IMAGE_FSTYPES = "container oci"

# Allow build with or without a specific kernel
IMAGE_CONTAINER_NO_DUMMY = "0"

# Container configuration
OCI_IMAGE_ENTRYPOINT = "/bin/bash"
OCI_IMAGE_ENV_VARS = " \
    CUDA_VERSION=12.6 \
    DEEPSTREAM_VERSION=7.1 \
    NVIDIA_VISIBLE_DEVICES=all \
    NVIDIA_DRIVER_CAPABILITIES=compute,video,utility \
    GST_PLUGIN_PATH=/opt/nvidia/deepstream/deepstream-7.1/lib/gst-plugins:/usr/lib/gstreamer-1.0 \
    LD_LIBRARY_PATH=/opt/nvidia/deepstream/deepstream-7.1/lib:/usr/local/cuda-12.6/lib64:/usr/lib \
    PATH=/opt/nvidia/deepstream/deepstream-7.1/bin:/usr/local/cuda-12.6/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin \
"

OCI_IMAGE_LABELS = " \
    org.opencontainers.image.title='DeepStream Container' \
    org.opencontainers.image.description='NVIDIA DeepStream 7.1 with CUDA 12.6 and GStreamer' \
    org.opencontainers.image.version='7.1-cuda12.6' \
    org.opencontainers.image.vendor='NVIDIA' \
    com.nvidia.deepstream.version='7.1' \
    com.nvidia.cuda.version='12.6' \
"

CONTAINER_SHELL = "bash"

# Enable package management for runtime package installation
IMAGE_FEATURES += "package-management"

# Core system packages
CORE_PACKAGES = " \
    bash \
    coreutils \
    util-linux \
    procps \
    psmisc \
    findutils \
    grep \
    sed \
    gawk \
    tar \
    gzip \
    bzip2 \
    xz \
    wget \
    curl \
    ca-certificates \
    openssl \
    tzdata \
    localedef \
"

# Development tools
DEV_PACKAGES = " \
    gcc \
    g++ \
    make \
    cmake \
    pkg-config \
    git \
    python3 \
    python3-dev \
    python3-pip \
    python3-setuptools \
    python3-wheel \
"

# CUDA and GPU packages
CUDA_PACKAGES = " \
    cuda-toolkit \
    cuda-toolkit-dev \
    tensorrt \
"

# DeepStream packages
DEEPSTREAM_PACKAGES = " \
    deepstream \
    deepstream-dev \
    deepstream-samples \
    deepstream-python \
"

# GStreamer packages
GSTREAMER_PACKAGES = " \
    gstreamer1.0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav \
    gstreamer1.0-rtsp-server \
    gstreamer1.0-vaapi \
    gstreamer1.0-plugins-deepstream \
    gstreamer1.0-tools \
"

# Python packages for DeepStream
PYTHON_PACKAGES = " \
    python3-numpy \
    python3-opencv \
    python3-pycairo \
    python3-pygobject \
    python3-pillow \
    python3-matplotlib \
    python3-scipy \
    python3-requests \
    python3-json \
"

# Multimedia and graphics libraries
MULTIMEDIA_PACKAGES = " \
    opencv \
    opencv-dev \
    ffmpeg \
    ffmpeg-dev \
    libdrm \
    libdrm-dev \
    mesa \
    mesa-dev \
    libx11 \
    libx11-dev \
    libxext \
    libxrandr \
    libxrender \
    libxinerama \
    libxcursor \
    libxi \
    libxss \
    libxcomposite \
    libxdamage \
    libxfixes \
    libxau \
    libxdmcp \
"

# Networking and communication
NETWORK_PACKAGES = " \
    openssh-client \
    rsync \
    netcat \
    iproute2 \
    iputils-ping \
    net-tools \
    tcpdump \
    nmap \
"

# JSON and configuration libraries
CONFIG_PACKAGES = " \
    json-glib \
    json-glib-dev \
    libxml2 \
    libxml2-dev \
    yaml-cpp \
    yaml-cpp-dev \
"

# Install all packages
IMAGE_INSTALL += " \
    ${CORE_PACKAGES} \
    ${DEV_PACKAGES} \
    ${CUDA_PACKAGES} \
    ${DEEPSTREAM_PACKAGES} \
    ${GSTREAMER_PACKAGES} \
    ${PYTHON_PACKAGES} \
    ${MULTIMEDIA_PACKAGES} \
    ${NETWORK_PACKAGES} \
    ${CONFIG_PACKAGES} \
"

# Post-install customizations
ROOTFS_POSTPROCESS_COMMAND += "deepstream_container_postprocess; "

deepstream_container_postprocess() {
    # Create necessary directories
    install -d ${IMAGE_ROOTFS}/workspace
    install -d ${IMAGE_ROOTFS}/models
    install -d ${IMAGE_ROOTFS}/data
    install -d ${IMAGE_ROOTFS}/output
    install -d ${IMAGE_ROOTFS}/tmp/gst-debug
    
    # Set up symbolic links for CUDA
    if [ ! -e ${IMAGE_ROOTFS}/usr/local/cuda ]; then
        ln -sf cuda-12.6 ${IMAGE_ROOTFS}/usr/local/cuda
    fi
    
    # Create container startup script
    cat > ${IMAGE_ROOTFS}/usr/local/bin/deepstream-init.sh << 'EOF'
#!/bin/bash
# DeepStream container initialization script

echo "Initializing DeepStream container..."

# Check NVIDIA runtime
if command -v nvidia-smi >/dev/null 2>&1; then
    echo "NVIDIA GPU detected:"
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader
else
    echo "Warning: NVIDIA GPU not detected or driver not available"
fi

# Test DeepStream installation
echo "Testing DeepStream installation..."
gst-deepstream-test.py --check

# Source environment
source /etc/profile.d/deepstream-gst-config.sh

echo "DeepStream container ready!"
echo "Available commands:"
echo "  gst-deepstream-test.py --help    - Test DeepStream functionality"
echo "  gst-launch-1.0                   - Run GStreamer pipelines"
echo "  deepstream-app                   - Run DeepStream reference app"
echo "  python3                          - Python with DeepStream bindings"

# If arguments provided, execute them
if [ $# -gt 0 ]; then
    exec "$@"
else
    exec /bin/bash
fi
EOF
    
    chmod +x ${IMAGE_ROOTFS}/usr/local/bin/deepstream-init.sh
    
    # Create sample configuration directory
    install -d ${IMAGE_ROOTFS}/workspace/configs
    
    # Set proper permissions
    chmod -R 755 ${IMAGE_ROOTFS}/workspace
    chmod -R 755 ${IMAGE_ROOTFS}/models
    chmod -R 755 ${IMAGE_ROOTFS}/data
    chmod -R 755 ${IMAGE_ROOTFS}/output
    
    # Update library cache
    if [ -x ${IMAGE_ROOTFS}${base_sbindir}/ldconfig ]; then
        ${IMAGE_ROOTFS}${base_sbindir}/ldconfig -r ${IMAGE_ROOTFS}
    fi
}

# Set the default command
OCI_IMAGE_ENTRYPOINT = "/usr/local/bin/deepstream-init.sh"

# Expose common ports for RTSP and web services
OCI_IMAGE_PORTS = "8554/tcp 8080/tcp 8000/tcp"

# Working directory
OCI_IMAGE_WORKINGDIR = "/workspace"

# Runtime user (can be overridden)
OCI_IMAGE_RUNTIME_UID = "1000"
