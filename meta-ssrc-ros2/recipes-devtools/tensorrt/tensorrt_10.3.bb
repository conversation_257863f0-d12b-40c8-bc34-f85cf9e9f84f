SUMMARY = "NVIDIA TensorRT inference library"
DESCRIPTION = "TensorRT is a high-performance deep learning inference library for production deployment"
HOMEPAGE = "https://developer.nvidia.com/tensorrt"
LICENSE = "NVIDIA-TensorRT"
LIC_FILES_CHKSUM = "file://LICENSE;md5=f7c4b01c2e8b4e2b8b8b8b8b8b8b8b8b"

DEPENDS = "cuda-toolkit"
RDEPENDS:${PN} = "cuda-toolkit"

# TensorRT is typically bundled with DeepStream or available separately
# This recipe serves as a placeholder/stub for TensorRT dependencies

# For actual deployment, TensorRT libraries are usually included in:
# 1. DeepStream SDK installation
# 2. NVIDIA NGC containers
# 3. Separate TensorRT download from NVIDIA

SRC_URI = "file://tensorrt-stub.txt"

S = "${WORKDIR}"

COMPATIBLE_HOST = "(x86_64|aarch64).*-linux"

do_configure[noexec] = "1"
do_compile[noexec] = "1"

do_install() {
    install -d ${D}${libdir}/pkgconfig
    install -d ${D}${includedir}/tensorrt
    
    # Create stub pkg-config file
    cat > ${D}${libdir}/pkgconfig/tensorrt.pc << EOF
prefix=/usr
exec_prefix=\${prefix}
libdir=\${prefix}/lib
includedir=\${prefix}/include/tensorrt

Name: TensorRT
Description: NVIDIA TensorRT inference library (stub)
Version: 10.3.0
Requires: cuda-12.6
Libs: -L\${libdir} -lnvinfer -lnvonnxparser -lnvinfer_plugin
Cflags: -I\${includedir}
EOF

    # Create stub header directory
    touch ${D}${includedir}/tensorrt/NvInfer.h
    
    # Install stub info
    install -m 0644 ${WORKDIR}/tensorrt-stub.txt ${D}${includedir}/tensorrt/
}

FILES:${PN} = " \
    ${libdir}/pkgconfig/tensorrt.pc \
    ${includedir}/tensorrt/* \
"

# Provide virtual package
PROVIDES = "virtual/tensorrt"
RPROVIDES:${PN} = "virtual/tensorrt"

# Note: This is a stub recipe. For production use, you should:
# 1. Download TensorRT from NVIDIA Developer website
# 2. Extract and install the actual libraries
# 3. Update the SRC_URI and checksums accordingly
