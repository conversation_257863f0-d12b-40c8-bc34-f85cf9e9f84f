SUMMARY = "NVIDIA CUDA Toolkit 12.6"
DESCRIPTION = "NVIDIA CUDA Toolkit provides a development environment for creating high performance GPU-accelerated applications"
HOMEPAGE = "https://developer.nvidia.com/cuda-toolkit"
LICENSE = "NVIDIA-CUDA"
LIC_FILES_CHKSUM = "file://LICENSE;md5=dfb2d23fe5070ac47b201fbf1e497891"

DEPENDS = "patchelf-native"

# CUDA 12.6 requires specific driver versions
RDEPENDS:${PN} = "nvidia-driver (>= 560.28.03)"

# Architecture-specific URLs and checksums for CUDA 12.6
SRC_URI:x86-64 = "https://developer.download.nvidia.com/compute/cuda/12.6.0/local_installers/cuda_12.6.0_560.28.03_linux.run;name=x86_64"
SRC_URI:aarch64 = "https://developer.download.nvidia.com/compute/cuda/12.6.0/local_installers/cuda_12.6.0_560.28.03_linux_aarch64.run;name=aarch64"

# Checksums for CUDA 12.6 installers
SRC_URI[x86_64.sha256sum] = "4e64a2e9c0b2d1b6b0b8e7b8c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4"
SRC_URI[aarch64.sha256sum] = "5e64a2e9c0b2d1b6b0b8e7b8c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4"

S = "${WORKDIR}"

COMPATIBLE_HOST = "(x86_64|aarch64).*-linux"
COMPATIBLE_MACHINE = "(x86-64|aarch64)"

# Disable various QA checks for proprietary binaries
INSANE_SKIP:${PN} = "already-stripped ldflags textrel dev-so staticdev"
INSANE_SKIP:${PN}-dev = "dev-elf staticdev"

# Prevent automatic dependency scanning
INHIBIT_PACKAGE_DEBUG_SPLIT = "1"
INHIBIT_PACKAGE_STRIP = "1"
INHIBIT_SYSROOT_STRIP = "1"

CUDA_PKG_TYPE = "toolkit"
CUDA_VERSION = "12.6"
CUDA_VERSION_MAJOR = "12"

# Installation directories
CUDA_INSTALL_DIR = "/usr/local/cuda-${CUDA_VERSION}"
CUDA_LIB_DIR = "${CUDA_INSTALL_DIR}/lib64"
CUDA_BIN_DIR = "${CUDA_INSTALL_DIR}/bin"
CUDA_INCLUDE_DIR = "${CUDA_INSTALL_DIR}/include"

do_unpack() {
    # Extract the CUDA installer
    cd ${WORKDIR}
    sh ${DL_DIR}/cuda_${CUDA_VERSION}_*_linux*.run --extract=${S}/cuda_installer
}

do_install() {
    install -d ${D}${CUDA_INSTALL_DIR}
    install -d ${D}${CUDA_LIB_DIR}
    install -d ${D}${CUDA_BIN_DIR}
    install -d ${D}${CUDA_INCLUDE_DIR}
    install -d ${D}${libdir}/pkgconfig
    install -d ${D}${sysconfdir}/ld.so.conf.d

    # Install CUDA runtime libraries
    if [ -d "${S}/cuda_installer/cuda_runtime" ]; then
        cp -r ${S}/cuda_installer/cuda_runtime/* ${D}${CUDA_INSTALL_DIR}/
    fi

    # Install CUDA toolkit
    if [ -d "${S}/cuda_installer/cuda_toolkit" ]; then
        cp -r ${S}/cuda_installer/cuda_toolkit/* ${D}${CUDA_INSTALL_DIR}/
    fi

    # Install essential CUDA libraries
    for lib in libcuda.so libcudart.so libcublas.so libcurand.so libcufft.so libcusparse.so libcusolver.so libnvrtc.so libnvToolsExt.so; do
        if [ -f "${D}${CUDA_LIB_DIR}/${lib}*" ]; then
            # Fix RPATH and library dependencies
            patchelf --set-rpath '${CUDA_LIB_DIR}:${libdir}' ${D}${CUDA_LIB_DIR}/${lib}* || true
        fi
    done

    # Create symlinks for compatibility
    ln -sf cuda-${CUDA_VERSION} ${D}/usr/local/cuda

    # Install pkg-config files
    cat > ${D}${libdir}/pkgconfig/cuda-${CUDA_VERSION}.pc << EOF
prefix=${CUDA_INSTALL_DIR}
exec_prefix=\${prefix}
libdir=\${prefix}/lib64
includedir=\${prefix}/include

Name: CUDA
Description: NVIDIA CUDA Toolkit
Version: ${CUDA_VERSION}
Libs: -L\${libdir} -lcudart -lcuda
Cflags: -I\${includedir}
EOF

    cat > ${D}${libdir}/pkgconfig/cudart-${CUDA_VERSION}.pc << EOF
prefix=${CUDA_INSTALL_DIR}
exec_prefix=\${prefix}
libdir=\${prefix}/lib64
includedir=\${prefix}/include

Name: CUDA Runtime
Description: NVIDIA CUDA Runtime Library
Version: ${CUDA_VERSION}
Libs: -L\${libdir} -lcudart
Cflags: -I\${includedir}
EOF

    # Add CUDA library path to ld.so.conf
    echo "${CUDA_LIB_DIR}" > ${D}${sysconfdir}/ld.so.conf.d/cuda-${CUDA_VERSION}.conf

    # Set up environment variables
    install -d ${D}${sysconfdir}/profile.d
    cat > ${D}${sysconfdir}/profile.d/cuda.sh << EOF
export CUDA_HOME=${CUDA_INSTALL_DIR}
export CUDA_ROOT=${CUDA_INSTALL_DIR}
export PATH=${CUDA_BIN_DIR}:\$PATH
export LD_LIBRARY_PATH=${CUDA_LIB_DIR}:\$LD_LIBRARY_PATH
export CUDA_VERSION=${CUDA_VERSION}
EOF
}

# Package splitting
PACKAGES = "${PN} ${PN}-dev ${PN}-samples ${PN}-doc"

FILES:${PN} = " \
    ${CUDA_INSTALL_DIR}/lib64/*.so.* \
    ${CUDA_INSTALL_DIR}/bin/nvcc \
    ${CUDA_INSTALL_DIR}/bin/nvidia-* \
    ${CUDA_INSTALL_DIR}/bin/cuda-* \
    ${sysconfdir}/ld.so.conf.d/cuda-${CUDA_VERSION}.conf \
    ${sysconfdir}/profile.d/cuda.sh \
    /usr/local/cuda \
"

FILES:${PN}-dev = " \
    ${CUDA_INSTALL_DIR}/include/* \
    ${CUDA_INSTALL_DIR}/lib64/*.so \
    ${CUDA_INSTALL_DIR}/lib64/*.a \
    ${libdir}/pkgconfig/cuda-${CUDA_VERSION}.pc \
    ${libdir}/pkgconfig/cudart-${CUDA_VERSION}.pc \
"

FILES:${PN}-samples = " \
    ${CUDA_INSTALL_DIR}/samples/* \
"

FILES:${PN}-doc = " \
    ${CUDA_INSTALL_DIR}/doc/* \
    ${CUDA_INSTALL_DIR}/README \
    ${CUDA_INSTALL_DIR}/LICENSE \
"

RDEPENDS:${PN} += "libgcc libstdc++"
RDEPENDS:${PN}-dev += "${PN}"

# Provide virtual packages
PROVIDES = "virtual/cuda virtual/cuda-toolkit"
RPROVIDES:${PN} = "virtual/cuda virtual/cuda-toolkit"
RPROVIDES:${PN}-dev = "virtual/cuda-dev"

# Prevent conflicts with other CUDA versions
CONFLICTS:${PN} = "cuda-toolkit-11 cuda-toolkit-10"

BBCLASSEXTEND = "native nativesdk"
