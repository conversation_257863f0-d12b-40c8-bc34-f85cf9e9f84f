SUMMARY = "NVIDIA DeepStream SDK 7.1"
DESCRIPTION = "NVIDIA DeepStream SDK is a complete streaming analytics toolkit for AI-based video and image understanding and multi-sensor processing"
HOMEPAGE = "https://developer.nvidia.com/deepstream-sdk"
LICENSE = "NVIDIA-DeepStream"
LIC_FILES_CHKSUM = "file://LICENSE.txt;md5=f7c4b01c2e8b4e2b8b8b8b8b8b8b8b8b"

DEPENDS = " \
    cuda-toolkit \
    gstreamer1.0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    glib-2.0 \
    json-glib \
    libxml2 \
    opencv \
    libdrm \
    libx11 \
    libxext \
    libxrandr \
    libxrender \
    libxinerama \
    libxcursor \
    libxi \
    libxss \
    libxcomposite \
    libxdamage \
    libxfixes \
    libxau \
    libxdmcp \
"

RDEPENDS:${PN} = " \
    cuda-toolkit \
    gstreamer1.0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-rtsp-server \
    python3 \
    python3-numpy \
    python3-opencv \
    python3-pycairo \
    python3-pygobject \
    libgstrtspserver-1.0 \
    nvidia-driver \
"

# DeepStream 7.1 download URLs
SRC_URI:x86-64 = "https://developer.nvidia.com/downloads/deepstream-7.1/deepstream_sdk_v7.1.0_x86_64.tbz2;name=x86_64"
SRC_URI:aarch64 = "https://developer.nvidia.com/downloads/deepstream-7.1/deepstream_sdk_v7.1.0_aarch64.tbz2;name=aarch64"

# Checksums (these would need to be updated with actual checksums)
SRC_URI[x86_64.sha256sum] = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
SRC_URI[aarch64.sha256sum] = "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

S = "${WORKDIR}/deepstream_sdk_v7.1.0_${TARGET_ARCH}"

COMPATIBLE_HOST = "(x86_64|aarch64).*-linux"
COMPATIBLE_MACHINE = "(x86-64|aarch64)"

# Disable QA checks for proprietary binaries
INSANE_SKIP:${PN} = "already-stripped ldflags textrel dev-so staticdev file-rdeps"
INSANE_SKIP:${PN}-dev = "dev-elf staticdev"
INSANE_SKIP:${PN}-python = "already-stripped"

INHIBIT_PACKAGE_DEBUG_SPLIT = "1"
INHIBIT_PACKAGE_STRIP = "1"
INHIBIT_SYSROOT_STRIP = "1"

DEEPSTREAM_VERSION = "7.1"
DEEPSTREAM_INSTALL_DIR = "/opt/nvidia/deepstream/deepstream-${DEEPSTREAM_VERSION}"
DEEPSTREAM_LIB_DIR = "${DEEPSTREAM_INSTALL_DIR}/lib"
DEEPSTREAM_BIN_DIR = "${DEEPSTREAM_INSTALL_DIR}/bin"
DEEPSTREAM_SAMPLES_DIR = "${DEEPSTREAM_INSTALL_DIR}/samples"

# CUDA paths from cuda-toolkit recipe
CUDA_VERSION = "12.6"
CUDA_INSTALL_DIR = "/usr/local/cuda-${CUDA_VERSION}"
CUDA_LIB_DIR = "${CUDA_INSTALL_DIR}/lib64"

do_configure[noexec] = "1"
do_compile[noexec] = "1"

do_install() {
    install -d ${D}${DEEPSTREAM_INSTALL_DIR}
    install -d ${D}${DEEPSTREAM_LIB_DIR}
    install -d ${D}${DEEPSTREAM_BIN_DIR}
    install -d ${D}${libdir}/gstreamer-1.0
    install -d ${D}${libdir}/pkgconfig
    install -d ${D}${includedir}/deepstream
    install -d ${D}${sysconfdir}/ld.so.conf.d
    install -d ${D}${sysconfdir}/profile.d
    install -d ${D}${datadir}/deepstream

    # Install DeepStream libraries
    cp -r ${S}/lib/* ${D}${DEEPSTREAM_LIB_DIR}/
    
    # Install DeepStream binaries
    if [ -d "${S}/bin" ]; then
        cp -r ${S}/bin/* ${D}${DEEPSTREAM_BIN_DIR}/
    fi

    # Install GStreamer plugins
    if [ -d "${S}/lib/gst-plugins" ]; then
        cp ${S}/lib/gst-plugins/*.so ${D}${libdir}/gstreamer-1.0/
    fi

    # Install headers
    if [ -d "${S}/sources/includes" ]; then
        cp -r ${S}/sources/includes/* ${D}${includedir}/deepstream/
    fi

    # Install samples and configs
    if [ -d "${S}/samples" ]; then
        cp -r ${S}/samples ${D}${DEEPSTREAM_INSTALL_DIR}/
    fi

    if [ -d "${S}/sources" ]; then
        cp -r ${S}/sources ${D}${DEEPSTREAM_INSTALL_DIR}/
    fi

    # Install Python bindings
    if [ -d "${S}/lib/pyds" ]; then
        install -d ${D}${PYTHON_SITEPACKAGES_DIR}
        cp -r ${S}/lib/pyds ${D}${PYTHON_SITEPACKAGES_DIR}/
    fi

    # Create pkg-config file
    cat > ${D}${libdir}/pkgconfig/deepstream.pc << EOF
prefix=${DEEPSTREAM_INSTALL_DIR}
exec_prefix=\${prefix}
libdir=\${prefix}/lib
includedir=${includedir}/deepstream
cuda_libdir=${CUDA_LIB_DIR}

Name: DeepStream
Description: NVIDIA DeepStream SDK
Version: ${DEEPSTREAM_VERSION}
Requires: gstreamer-1.0 >= 1.20.0, cuda-${CUDA_VERSION}
Libs: -L\${libdir} -L\${cuda_libdir} -lnvdsgst_meta -lnvds_meta -lnvdsgst_helper -lnvdsgst_smartrecord -lnvds_utils -lnvds_msgbroker -lcudart
Cflags: -I\${includedir} -I${CUDA_INSTALL_DIR}/include
EOF

    # Add library path to ld.so.conf
    echo "${DEEPSTREAM_LIB_DIR}" > ${D}${sysconfdir}/ld.so.conf.d/deepstream.conf

    # Environment setup script
    cat > ${D}${sysconfdir}/profile.d/deepstream.sh << EOF
export DEEPSTREAM_PATH=${DEEPSTREAM_INSTALL_DIR}
export CUDA_HOME=${CUDA_INSTALL_DIR}
export CUDA_ROOT=${CUDA_INSTALL_DIR}
export LD_LIBRARY_PATH=${DEEPSTREAM_LIB_DIR}:${CUDA_LIB_DIR}:\$LD_LIBRARY_PATH
export GST_PLUGIN_PATH=${libdir}/gstreamer-1.0:\$GST_PLUGIN_PATH
export PATH=${DEEPSTREAM_BIN_DIR}:${CUDA_INSTALL_DIR}/bin:\$PATH
EOF

    # Fix library permissions and RPATH
    find ${D}${DEEPSTREAM_LIB_DIR} -name "*.so*" -exec chmod 755 {} \;
    find ${D}${libdir}/gstreamer-1.0 -name "*.so" -exec chmod 755 {} \;
    
    # Fix RPATH for DeepStream libraries
    for lib in ${D}${DEEPSTREAM_LIB_DIR}/*.so*; do
        if [ -f "$lib" ]; then
            patchelf --set-rpath '${DEEPSTREAM_LIB_DIR}:${libdir}:${CUDA_LIB_DIR}' "$lib" || true
        fi
    done

    # Fix RPATH for GStreamer plugins
    for plugin in ${D}${libdir}/gstreamer-1.0/*.so; do
        if [ -f "$plugin" ]; then
            patchelf --set-rpath '${DEEPSTREAM_LIB_DIR}:${libdir}:${CUDA_LIB_DIR}' "$plugin" || true
        fi
    done
}

# Package splitting
PACKAGES = "${PN} ${PN}-dev ${PN}-samples ${PN}-python ${PN}-doc"

FILES:${PN} = " \
    ${DEEPSTREAM_LIB_DIR}/*.so.* \
    ${DEEPSTREAM_BIN_DIR}/* \
    ${libdir}/gstreamer-1.0/*.so \
    ${sysconfdir}/ld.so.conf.d/deepstream.conf \
    ${sysconfdir}/profile.d/deepstream.sh \
    ${datadir}/deepstream \
"

FILES:${PN}-dev = " \
    ${DEEPSTREAM_LIB_DIR}/*.so \
    ${includedir}/deepstream/* \
    ${libdir}/pkgconfig/deepstream.pc \
"

FILES:${PN}-samples = " \
    ${DEEPSTREAM_SAMPLES_DIR}/* \
    ${DEEPSTREAM_INSTALL_DIR}/sources/* \
"

FILES:${PN}-python = " \
    ${PYTHON_SITEPACKAGES_DIR}/pyds/* \
"

FILES:${PN}-doc = " \
    ${DEEPSTREAM_INSTALL_DIR}/doc/* \
    ${DEEPSTREAM_INSTALL_DIR}/*.txt \
    ${DEEPSTREAM_INSTALL_DIR}/README* \
"

RDEPENDS:${PN}-dev += "${PN}"
RDEPENDS:${PN}-samples += "${PN} ${PN}-dev"
RDEPENDS:${PN}-python += "${PN} python3"

# Virtual package provides
PROVIDES = "virtual/deepstream"
RPROVIDES:${PN} = "virtual/deepstream"

inherit python3-dir
