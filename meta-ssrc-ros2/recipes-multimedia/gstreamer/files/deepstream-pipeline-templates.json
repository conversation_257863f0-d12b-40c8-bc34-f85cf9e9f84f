{"deepstream_pipelines": {"basic_inference": {"description": "Basic DeepStream inference pipeline", "pipeline": "filesrc location={input_file} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "required_params": ["input_file", "config_file"]}, "rtsp_inference": {"description": "RTSP stream with DeepStream inference", "pipeline": "rtspsrc location={rtsp_url} latency=0 ! rtph264depay ! h264parse ! nvv4l2decoder ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "required_params": ["rtsp_url", "config_file"]}, "multi_stream_inference": {"description": "Multi-stream DeepStream inference", "pipeline": "nvstreammux name=mux batch-size={batch_size} width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvvideoconvert ! nvdsosd ! nvtiler rows={tiler_rows} columns={tiler_cols} ! nvegltransform ! nveglglessink", "required_params": ["batch_size", "config_file", "tiler_rows", "tiler_cols"], "sources": ["filesrc location={input_file_1} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! mux.sink_0", "filesrc location={input_file_2} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! mux.sink_1"]}, "camera_inference": {"description": "Camera input with DeepStream inference", "pipeline": "nvarguscamerasrc sensor-id={sensor_id} ! video/x-raw(memory:NVMM),width=1920,height=1080,framerate=30/1,format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "required_params": ["sensor_id", "config_file"]}, "inference_with_tracking": {"description": "DeepStream inference with object tracking", "pipeline": "filesrc location={input_file} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={primary_config} ! nvtracker tracker-width=640 tracker-height=384 ll-lib-file=/opt/nvidia/deepstream/deepstream-7.1/lib/libnvds_nvmultiobjecttracker.so ll-config-file={tracker_config} ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "required_params": ["input_file", "primary_config", "tracker_config"]}, "rtsp_output": {"description": "DeepStream inference with RTSP output", "pipeline": "filesrc location={input_file} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvvideoconvert ! nvdsosd ! nvvideoconvert ! video/x-raw,format=I420 ! x264enc bitrate=4000 ! rtph264pay ! udpsink host={output_host} port={output_port}", "required_params": ["input_file", "config_file", "output_host", "output_port"]}, "file_output": {"description": "DeepStream inference with file output", "pipeline": "filesrc location={input_file} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvvideoconvert ! nvdsosd ! nvvideoconvert ! video/x-raw,format=I420 ! x264enc ! mp4mux ! filesink location={output_file}", "required_params": ["input_file", "config_file", "output_file"]}, "secondary_inference": {"description": "Primary and secondary inference pipeline", "pipeline": "filesrc location={input_file} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer name=primary config-file-path={primary_config} ! nvinfer name=secondary process-mode=2 config-file-path={secondary_config} ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "required_params": ["input_file", "primary_config", "secondary_config"]}, "analytics_pipeline": {"description": "DeepStream analytics with message broker", "pipeline": "filesrc location={input_file} ! decodebin ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path={config_file} ! nvtracker tracker-width=640 tracker-height=384 ll-lib-file=/opt/nvidia/deepstream/deepstream-7.1/lib/libnvds_nvmultiobjecttracker.so ll-config-file={tracker_config} ! nvdsanalytics config-file={analytics_config} ! nvmsgconv config={msgconv_config} payload-type=0 ! nvmsgbroker config={broker_config} ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "required_params": ["input_file", "config_file", "tracker_config", "analytics_config", "msgconv_config", "broker_config"]}}, "common_configs": {"resnet10_primary": "/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt", "yolo_primary": "/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary_yoloV5.txt", "tracker_config": "/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_tracker_NvDCF_perf.yml", "analytics_config": "/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_nvdsanalytics.txt"}, "sample_commands": {"test_basic": "gst-launch-1.0 videotestsrc num-buffers=300 ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1920 height=1080 ! nvinfer config-file-path=/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink", "test_file": "gst-launch-1.0 filesrc location=/opt/nvidia/deepstream/deepstream-7.1/samples/streams/sample_720p.h264 ! h264parse ! nvv4l2decoder ! nvvideoconvert ! video/x-raw(memory:NVMM),format=NV12 ! nvstreammux name=mux batch-size=1 width=1280 height=720 ! nvinfer config-file-path=/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt ! nvvideoconvert ! nvdsosd ! nvegltransform ! nveglglessink"}}