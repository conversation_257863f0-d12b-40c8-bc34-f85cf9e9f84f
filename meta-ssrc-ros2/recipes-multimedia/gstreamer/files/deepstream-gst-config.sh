#!/bin/bash
# GStreamer configuration for DeepStream applications

# Set GStreamer plugin paths for DeepStream
export GST_PLUGIN_PATH="/opt/nvidia/deepstream/deepstream-7.1/lib/gst-plugins:${GST_PLUGIN_PATH}"
export GST_PLUGIN_PATH="/usr/lib/gstreamer-1.0:${GST_PLUGIN_PATH}"

# DeepStream specific environment variables
export DEEPSTREAM_PATH="/opt/nvidia/deepstream/deepstream-7.1"
export NVDS_VERSION="7.1"

# CUDA paths for DeepStream
export CUDA_VER="12.6"
export CUDA_HOME="/usr/local/cuda-${CUDA_VER}"
export CUDA_ROOT="${CUDA_HOME}"

# Library paths
export LD_LIBRARY_PATH="${DEEPSTREAM_PATH}/lib:${LD_LIBRARY_PATH}"
export LD_LIBRARY_PATH="${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}"

# GStreamer debug and performance settings for DeepStream
export GST_DEBUG_NO_COLOR=1
export GST_DEBUG_DUMP_DOT_DIR="/tmp/gst-debug"

# Create debug directory if it doesn't exist
mkdir -p "${GST_DEBUG_DUMP_DOT_DIR}"

# DeepStream specific GStreamer registry
export GST_REGISTRY="/tmp/gst-registry-deepstream.bin"

# Performance optimizations
export GST_GL_PLATFORM=egl
export GST_GL_API=gles2
export GST_GL_WINDOW=wayland

# Function to test DeepStream GStreamer installation
test_deepstream_gst() {
    echo "Testing DeepStream GStreamer installation..."
    
    # Check if DeepStream plugins are available
    gst-inspect-1.0 nvvideoconvert >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ DeepStream nvvideoconvert plugin found"
    else
        echo "✗ DeepStream nvvideoconvert plugin not found"
    fi
    
    gst-inspect-1.0 nvinfer >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ DeepStream nvinfer plugin found"
    else
        echo "✗ DeepStream nvinfer plugin not found"
    fi
    
    gst-inspect-1.0 nvstreammux >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ DeepStream nvstreammux plugin found"
    else
        echo "✗ DeepStream nvstreammux plugin not found"
    fi
    
    # Check CUDA availability
    if command -v nvidia-smi >/dev/null 2>&1; then
        echo "✓ NVIDIA driver available"
        nvidia-smi --query-gpu=name,driver_version,cuda_version --format=csv,noheader
    else
        echo "✗ NVIDIA driver not available"
    fi
}

# Function to run a simple DeepStream test pipeline
test_deepstream_pipeline() {
    echo "Running DeepStream test pipeline..."
    
    # Simple test pipeline using videotestsrc
    gst-launch-1.0 \
        videotestsrc num-buffers=100 ! \
        'video/x-raw,width=1920,height=1080,framerate=30/1' ! \
        nvvideoconvert ! \
        'video/x-raw(memory:NVMM),format=NV12' ! \
        nvstreammux name=mux batch-size=1 width=1920 height=1080 ! \
        nvinfer config-file-path=/opt/nvidia/deepstream/deepstream-7.1/samples/configs/deepstream-app/config_infer_primary.txt ! \
        nvvideoconvert ! \
        nvdsosd ! \
        nvegltransform ! \
        nveglglessink
}

# Export functions for use in shell
export -f test_deepstream_gst
export -f test_deepstream_pipeline

echo "DeepStream GStreamer environment configured"
echo "Run 'test_deepstream_gst' to test the installation"
echo "Run 'test_deepstream_pipeline' to test a simple pipeline"
