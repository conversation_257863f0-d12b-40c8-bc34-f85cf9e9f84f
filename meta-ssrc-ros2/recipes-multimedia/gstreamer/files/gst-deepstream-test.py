#!/usr/bin/env python3
"""
DeepStream GStreamer Test Script
Tests DeepStream installation and provides sample pipeline execution
"""

import sys
import os
import json
import subprocess
import argparse
import gi

gi.require_version('Gst', '1.0')
from gi import require_version
require_version('Gst', '1.0')
from gi.repository import Gst, GLib

class DeepStreamTester:
    def __init__(self):
        Gst.init(None)
        self.pipeline = None
        self.loop = None
        
    def check_deepstream_plugins(self):
        """Check if DeepStream plugins are available"""
        required_plugins = [
            'nvvideoconvert',
            'nvstreammux',
            'nvinfer',
            'nvtracker',
            'nvdsosd',
            'nvegltransform',
            'nveglglessink',
            'nvv4l2decoder',
            'nvv4l2encoder'
        ]
        
        print("Checking DeepStream plugins...")
        missing_plugins = []
        
        for plugin in required_plugins:
            registry = Gst.Registry.get()
            feature = registry.find_feature(plugin, Gst.ElementFactory.__gtype__)
            if feature:
                print(f"✓ {plugin} - available")
            else:
                print(f"✗ {plugin} - missing")
                missing_plugins.append(plugin)
        
        return len(missing_plugins) == 0, missing_plugins
    
    def check_cuda_availability(self):
        """Check CUDA and NVIDIA driver availability"""
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ NVIDIA driver available")
                print("GPU Information:")
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'NVIDIA-SMI' in line or 'Driver Version' in line:
                        print(f"  {line.strip()}")
                return True
            else:
                print("✗ NVIDIA driver not available")
                return False
        except FileNotFoundError:
            print("✗ nvidia-smi not found")
            return False
    
    def run_test_pipeline(self, pipeline_str):
        """Run a test pipeline"""
        print(f"Running pipeline: {pipeline_str}")
        
        try:
            self.pipeline = Gst.parse_launch(pipeline_str)
            bus = self.pipeline.get_bus()
            bus.add_signal_watch()
            bus.connect("message", self.on_message)
            
            # Start pipeline
            ret = self.pipeline.set_state(Gst.State.PLAYING)
            if ret == Gst.StateChangeReturn.FAILURE:
                print("Failed to start pipeline")
                return False
            
            # Run main loop
            self.loop = GLib.MainLoop()
            try:
                self.loop.run()
            except KeyboardInterrupt:
                print("Interrupted by user")
            
            # Cleanup
            self.pipeline.set_state(Gst.State.NULL)
            return True
            
        except Exception as e:
            print(f"Error running pipeline: {e}")
            return False
    
    def on_message(self, bus, message):
        """Handle GStreamer messages"""
        t = message.type
        if t == Gst.MessageType.EOS:
            print("End of stream")
            self.loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}, {debug}")
            self.loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}, {debug}")
    
    def load_pipeline_templates(self):
        """Load pipeline templates from JSON file"""
        template_file = "/usr/share/deepstream/templates/deepstream-pipeline-templates.json"
        try:
            with open(template_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Template file not found: {template_file}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error parsing template file: {e}")
            return None
    
    def list_available_pipelines(self):
        """List available pipeline templates"""
        templates = self.load_pipeline_templates()
        if not templates:
            return
        
        print("Available pipeline templates:")
        for name, config in templates.get('deepstream_pipelines', {}).items():
            print(f"  {name}: {config.get('description', 'No description')}")
            if 'required_params' in config:
                print(f"    Required parameters: {', '.join(config['required_params'])}")
    
    def run_sample_pipeline(self, pipeline_name):
        """Run a sample pipeline from templates"""
        templates = self.load_pipeline_templates()
        if not templates:
            return False
        
        sample_commands = templates.get('sample_commands', {})
        if pipeline_name in sample_commands:
            return self.run_test_pipeline(sample_commands[pipeline_name])
        else:
            print(f"Sample pipeline '{pipeline_name}' not found")
            print("Available samples:", list(sample_commands.keys()))
            return False

def main():
    parser = argparse.ArgumentParser(description='DeepStream GStreamer Test Tool')
    parser.add_argument('--check', action='store_true', help='Check DeepStream installation')
    parser.add_argument('--list-pipelines', action='store_true', help='List available pipeline templates')
    parser.add_argument('--run-sample', type=str, help='Run a sample pipeline (test_basic, test_file)')
    parser.add_argument('--run-pipeline', type=str, help='Run a custom GStreamer pipeline')
    
    args = parser.parse_args()
    
    tester = DeepStreamTester()
    
    if args.check:
        print("=== DeepStream Installation Check ===")
        plugins_ok, missing = tester.check_deepstream_plugins()
        cuda_ok = tester.check_cuda_availability()
        
        if plugins_ok and cuda_ok:
            print("\n✓ DeepStream installation appears to be working correctly")
        else:
            print("\n✗ DeepStream installation has issues")
            if missing:
                print(f"Missing plugins: {', '.join(missing)}")
        
        return 0 if (plugins_ok and cuda_ok) else 1
    
    elif args.list_pipelines:
        tester.list_available_pipelines()
        return 0
    
    elif args.run_sample:
        success = tester.run_sample_pipeline(args.run_sample)
        return 0 if success else 1
    
    elif args.run_pipeline:
        success = tester.run_test_pipeline(args.run_pipeline)
        return 0 if success else 1
    
    else:
        parser.print_help()
        return 1

if __name__ == '__main__':
    sys.exit(main())
