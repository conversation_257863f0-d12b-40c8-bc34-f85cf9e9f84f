SUMMARY = "GStreamer plugins optimized for DeepStream applications"
DESCRIPTION = "Additional GStreamer plugins and configurations optimized for NVIDIA DeepStream SDK usage"
HOMEPAGE = "https://developer.nvidia.com/deepstream-sdk"
LICENSE = "LGPL-2.1-or-later"
LIC_FILES_CHKSUM = "file://${COMMON_LICENSE_DIR}/LGPL-2.1;md5=1a6d268fd218675ffea8be556788b780"

DEPENDS = " \
    gstreamer1.0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-rtsp-server \
    cuda-toolkit \
    deepstream \
    opencv \
    json-glib \
    libdrm \
    libx11 \
"

RDEPENDS:${PN} = " \
    gstreamer1.0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-rtsp-server \
    deepstream \
"

# This is a configuration-only recipe
SRC_URI = " \
    file://deepstream-gst-config.sh \
    file://deepstream-pipeline-templates.json \
    file://gst-deepstream-test.py \
"

S = "${WORKDIR}"

do_configure[noexec] = "1"
do_compile[noexec] = "1"

do_install() {
    install -d ${D}${sysconfdir}/profile.d
    install -d ${D}${datadir}/deepstream/configs
    install -d ${D}${datadir}/deepstream/templates
    install -d ${D}${bindir}
    install -d ${D}${libdir}/pkgconfig

    # Install GStreamer configuration for DeepStream
    install -m 0755 ${WORKDIR}/deepstream-gst-config.sh ${D}${sysconfdir}/profile.d/

    # Install pipeline templates
    install -m 0644 ${WORKDIR}/deepstream-pipeline-templates.json ${D}${datadir}/deepstream/templates/

    # Install test script
    install -m 0755 ${WORKDIR}/gst-deepstream-test.py ${D}${bindir}/

    # Create pkg-config for GStreamer DeepStream integration
    cat > ${D}${libdir}/pkgconfig/gstreamer-deepstream.pc << EOF
prefix=/usr
exec_prefix=\${prefix}
libdir=\${prefix}/lib
includedir=\${prefix}/include
plugindir=\${libdir}/gstreamer-1.0

Name: GStreamer DeepStream Integration
Description: GStreamer plugins and configurations for DeepStream
Version: 1.20.3
Requires: gstreamer-1.0 >= 1.20.0, gstreamer-plugins-base-1.0 >= 1.20.0
Libs: -L\${libdir}
Cflags: -I\${includedir}
EOF
}

FILES:${PN} = " \
    ${sysconfdir}/profile.d/deepstream-gst-config.sh \
    ${datadir}/deepstream/configs/* \
    ${datadir}/deepstream/templates/* \
    ${bindir}/gst-deepstream-test.py \
    ${libdir}/pkgconfig/gstreamer-deepstream.pc \
"

RDEPENDS:${PN} += "python3 python3-pygobject"
