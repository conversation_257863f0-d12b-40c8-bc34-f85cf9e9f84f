on:
  workflow_call:
    inputs:
      arch:
        required: true
        type: string
      publish_in_digitalocean:
        required: true
        type: boolean
    secrets:
      ACCESS_REPO_TOKEN:
        required: true

env:
  REPO_NAME: drone-yocto
  IMAGE_RECIPE_NAME: fog-deb-package-build-image

jobs:
  build:
    runs-on: dlabbuilder-yocto-${{ inputs.arch }}
    container:
      image: ubuntu:22.04
      volumes:
        - /home/<USER>/.ssh:/root/.ssh

    steps:
      - name: print environment variables
        run: |
          echo "REPO_NAME: $REPO_NAME"
          echo "IMAGE_RECIPE_NAME: $IMAGE_RECIPE_NAME"
          echo "ARCH: ${{ inputs.arch }}"
          echo "PUSH_TO_ARTIFACTORY: ${{ inputs.push_to_artifactory }}"
          echo "PUSH_TO_DIGITAL_OCEAN: ${{ inputs.push_to_digital_ocean }}"
          echo "ACCESS_REPO_TOKEN: $ACCESS_REPO_TOKEN"
      - name: Run yocto distro build
        run: |
          apt update
          # installing t<PERSON><PERSON> will ask for timezone, so we set it to noninteractive
          DEBIAN_FRONTEND=noninteractive apt install -y --no-install-recommends build-essential chrpath cpio curl \
            diffstat file gawk gcc-multilib git-core jq libegl1-mesa libffi-dev liblz4-tool \
            libsdl1.2-dev libssl-dev locales lz4 mesa-common-dev openssh-client python3 \
            python3-dev python3-git python3-jinja2 python3-pexpect python3-pip python3 subunit \
            python3-venv rsync socat ssh texinfo unzip wget xterm xxd xz-utils zstd
          locale-gen en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
          git config --global --add safe.directory '*'
      - name: Checkout repo and its submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          set-safe-directory: '*'
          token: ${{ secrets.ACCESS_REPO_TOKEN }}
      - name: Run yocto distro build
        run: |
          export HOME=/root

          git config --global --add safe.directory '*'

          # because gh runs sh instead of bash we have to use . instead of "source" and set variable
          set -- build-${{ inputs.arch }}
          TEMPLATECONF=meta-ssrc-ros2/conf/${{ inputs.arch }}-template . ./oe-init-build-env build-${{ inputs.arch }}
          DISTRO=drone bitbake ${{ env.IMAGE_RECIPE_NAME }}

      - name: upload deb packages to NAS temporarily
        if: ${{ inputs.publish_in_digitalocean }}
        uses: tiiuae/drone-yocto-deb-uploader@867d468a6e0a89a2d928709ff561fe65499d1396
        # dont fail build if upload to nas fails as it is still wip
        continue-on-error: true
        with:
          FILE: '*.deb'
          BASEDIR: build-${{ inputs.arch }}/tmp-glibc/deploy/deb
          DIR: '*'
          DISTRIBUTION: 'humble'
          COMPONENT: ${GITHUB_REF##*/}
          ARCH: ${{ inputs.arch }}
