name: drone-yocto-minimal-base-image-build

on:
  push:
    branches:
      - "main"
    paths-ignore:
      - "README.md"
  pull_request:
  workflow_dispatch:

env:
  REPO_NAME: drone-yocto
  IMAGE_RECIPE_NAME: fog-minimal-container-image

permissions:
  contents: read
  packages: write

jobs:
  yocto-build:
    strategy:
      fail-fast: false
      matrix:
        arch: [amd64, arm64, riscv64]
    uses: ./.github/workflows/drone-yocto-minimal-base-image-common.yaml
    with:
      arch: ${{ matrix.arch }}
    secrets: inherit

  generate-manifest-list:
    runs-on: ubuntu-22.04
    needs: yocto-build
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create manifest list
        run: |
          docker buildx imagetools create \
          -t ghcr.io/tiiuae/${{ env.IMAGE_RECIPE_NAME }}:${{ needs.yocto-build.outputs.short_sha }} \
          ghcr.io/tiiuae/${{ env.IMAGE_RECIPE_NAME }}:${{ needs.yocto-build.outputs.short_sha }}-amd64 \
          ghcr.io/tiiuae/${{ env.IMAGE_RECIPE_NAME }}:${{ needs.yocto-build.outputs.short_sha }}-arm64 \
          ghcr.io/tiiuae/${{ env.IMAGE_RECIPE_NAME }}:${{ needs.yocto-build.outputs.short_sha }}-riscv64
