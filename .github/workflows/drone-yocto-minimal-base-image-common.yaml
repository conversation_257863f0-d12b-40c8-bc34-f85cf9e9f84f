on:
  workflow_call:
    inputs:
      arch:
        required: true
        type: string
    outputs:
      short_sha:
        value: ${{ jobs.yocto-build.outputs.short_sha }}
    secrets:
      ACCESS_REPO_TOKEN:
        required: true

env:
  REPO_NAME: drone-yocto
  IMAGE_RECIPE_NAME: fog-minimal-container-image


permissions:
  contents: read
  packages: write

jobs:
  yocto-build:
    runs-on: dlabbuilder-yocto-${{ inputs.arch }}
    container:
      # TODO: use ubuntu 22.04. ROS docker image should not be needed anymore.
      # rclgo has a fix which removed this bad dependency.
      image: ros:galactic-ros-base
    outputs:
      short_sha: ${{ steps.vars.outputs.short_sha }}
    steps:
      - name: Install build tools
        run: |
          apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys 4B63CF8FDE49746E98FA01DDAD19BAB3CBF125EA
          apt update
          apt install -y --no-install-recommends build-essential chrpath cpio curl debianutils \
            diffstat file gawk gcc-multilib git-core jq libegl1-mesa libffi-dev liblz4-tool \
            libsdl1.2-dev libssl-dev locales lz4 mesa-common-dev openssh-client pylint3 python3 \
            python3-dev python3-git python3-jinja2 python3-pexpect python3-pip python3 subunit \
            python3-venv socat texinfo unzip wget xterm xxd xz-utils zstd
          locale-gen en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
          git config --global --add safe.directory '*'

      - name: Checkout repo and its submodules
        uses: actions/checkout@v4
        with:
          submodules: 'true'
          set-safe-directory: '*'
          token: ${{ secrets.ACCESS_REPO_TOKEN }}

      - name: Credentials
        env:
          ID: ${{ secrets.ARTIFACTORY_CACHE_ACCESS_ID }}
          TOKEN: ${{ secrets.ARTIFACTORY_CACHE_ACCESS_PW }}
        run: |
          cat > ~/.netrc <<EOF
            machine ssrc.jfrog.io
              login $ID
              password $TOKEN
          EOF
          chmod 600 ~/.netrc
          cp ~/.netrc /root/.netrc

      - name: Set short git commit SHA
        id: vars
        run: |
          calculatedSha=$(git rev-parse --short ${{ github.sha }})
          echo "short_sha=sha-$calculatedSha" >> $GITHUB_OUTPUT

      - name: Run yocto distro build
        run: |
          export HOME=/root
          # gh runs sh instead of bash so we have to use . instead of "source"
          # use set variable because sh . -operator doesnt accept arguments
          set -- build-${{ inputs.arch }}
          TEMPLATECONF=meta-ssrc-ros2/conf/${{ inputs.arch }}-template . ./oe-init-build-env
          DISTRO=drone bitbake ${{ env.IMAGE_RECIPE_NAME }}

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.REPO_NAME }}-${{ inputs.arch }}-deploy-images
          path: build-${{ inputs.arch }}/tmp-glibc/deploy/images/container-${{ inputs.arch }}/*.rootfs-oci
          retention-days: 1

  push-image-to-ghcr:
    runs-on: dlabbuilder-yocto-${{ inputs.arch }}
    needs: yocto-build
    steps:
      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ env.REPO_NAME }}-${{ inputs.arch }}-deploy-images
          path: images/

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload docker image to GHCR
        run: |
          skopeo --debug copy --insecure-policy -f v2s2 oci:$(find images -iname "*${{ inputs.arch }}*.rootfs-oci") \
            docker://ghcr.io/tiiuae/${{ env.IMAGE_RECIPE_NAME }}:${{ needs.yocto-build.outputs.short_sha }}-${{ inputs.arch }}
