name: drone-yocto-sstate-cache-sources

on:
  workflow_dispatch:
  schedule:
    # Runs "At 19:00 on every Friday"
    - cron: '0 19 * * 5'
env:
  REPO_NAME: drone-yocto
  IMAGE_RECIPE_NAME: fog-deb-package-build-image
  NAS_ADDRESS: upmknas
  NAS_USER: devops
  NAS_SHARE_PATH: /share/drone-yocto-sstate-cache

jobs:
  upload-sources:
    # this needs to be ran only once, so we set it to run on amd64 builder (could be any other builder as well)
    runs-on: dlabbuilder-yocto-amd64
    container:
      image: ubuntu:22.04
      volumes:
        - /home/<USER>/.ssh:/root/.ssh
    steps:
      - name: install dependencies
        run: |
          apt update
          # installing tzda<PERSON> will ask for timezone, so we set it to noninteractive
          DEBIAN_FRONTEND=noninteractive apt install -y --no-install-recommends build-essential chrpath cpio curl \
            diffstat file gawk gcc-multilib git-core jq libegl1-mesa libffi-dev liblz4-tool \
            libsdl1.2-dev libssl-dev locales lz4 mesa-common-dev openssh-client python3 \
            python3-dev python3-git python3-jinja2 python3-pexpect python3-pip python3 subunit \
            python3-venv socat texinfo unzip wget xterm xxd xz-utils zstd rsync

          git config --global --add safe.directory '*'
          locale-gen en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

      - name: Checkout repo and its submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.ACCESS_REPO_TOKEN }}

      - name: Configure and build
        run: |
          set -- build-amd64
          TEMPLATECONF=meta-ssrc-ros2/conf/amd64-template . ./oe-init-build-env
          # SOURCE_MIRROR_FETCH is needed when fetching sources for the source mirror.
          # See: https://docs.yoctoproject.org/ref-manual/variables.html#term-SOURCE_MIRROR_FETCH
          echo "SOURCE_MIRROR_FETCH = \"1\"" >> conf/local.conf
          bitbake --runall=fetch ${{ env.IMAGE_RECIPE_NAME }}

      - name: Upload source packages to upmknas
        env:
          DESTINATION_SOURCES_PATH: ${{ env.NAS_SHARE_PATH }}/sources
        run: |
          rsync_file() {
            local file=${1}
            local dest=${2}
            local dest_url="${{ env.NAS_USER }}@${{ env.NAS_ADDRESS }}:${dest}"
            echo "rsyncing ${file} to ${dest_url}"
            rsync -av --progress $file $dest_url
          }

          set -exu
          cd downloads
          rsync_file "*.tar.bz2" \
                    "${{ env.DESTINATION_SOURCES_PATH }}"
          rsync_file "*.tar.gz" \
                      "${{ env.DESTINATION_SOURCES_PATH }}"
          rsync_file "*.tar.xz" \
                      "${{ env.DESTINATION_SOURCES_PATH }}"
          rsync_file "*.crate" \
                      "${{ env.DESTINATION_SOURCES_PATH }}"

  yocto-image-full-build:
    needs: upload-sources
    runs-on: dlabbuilder-yocto-${{ matrix.arch }}
    container:
      image: ubuntu:22.04
      volumes:
        - /home/<USER>/.ssh:/root/.ssh
    strategy:
      fail-fast: false
      matrix:
        arch: [riscv64, amd64, arm64]

    steps:
      - name: install dependencies
        run: |
          apt update
          # installing tzdata will ask for timezone, so we set it to noninteractive
          DEBIAN_FRONTEND=noninteractive apt install -y --no-install-recommends build-essential chrpath cpio curl \
            diffstat file gawk gcc-multilib git-core jq libegl1-mesa libffi-dev liblz4-tool \
            libsdl1.2-dev libssl-dev locales lz4 mesa-common-dev openssh-client python3 \
            python3-dev python3-git python3-jinja2 python3-pexpect python3-pip python3 subunit \
            python3-venv socat texinfo unzip wget xterm xxd xz-utils zstd rsync

          git config --global --add safe.directory '*'
          locale-gen en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

      - name: Checkout repo and its submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.ACCESS_REPO_TOKEN }}

      - name: Run yocto distro build
        run: |
          set -- build-${{ matrix.arch }}
          TEMPLATECONF=meta-ssrc-ros2/conf/${{ matrix.arch }}-template . ./oe-init-build-env build-${{ matrix.arch }}
          bitbake --no-setscene ${{ env.IMAGE_RECIPE_NAME }}

      - name: Upload sstate-cache to upmknas
        env:
          DESTINATION_SSTATE_CACHE_PATH: ${{ env.NAS_SHARE_PATH }}/yocto/kirkstone/${{ matrix.arch }}
        run: |
          set -exu
          # devops@upmknas:/share/drone-yocto-sstate-cache/yocto/kirkstone/[arch]
          dest_url="${{ env.NAS_USER }}@${{ env.NAS_ADDRESS }}:${{ env.DESTINATION_SSTATE_CACHE_PATH }}"

          cd build-${{ matrix.arch }}

          echo "running rsync sstate-cache to ${dest_url}"
          rsync -av --progress sstate-cache ${dest_url}
