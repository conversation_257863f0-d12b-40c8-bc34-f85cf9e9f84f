on:
  workflow_call:
    inputs:
      arch:
        required: true
        type: string
    secrets:
      ACCESS_REPO_TOKEN:
        required: true

env:
  REPO_NAME: drone-yocto
  IMAGE_RECIPE_NAME: fog-image-sdk

jobs:
  build:
    runs-on: dlabbuilder-yocto-${{ inputs.arch }}
    container:
      image: ubuntu:22.04
      volumes:
        - /home/<USER>/.ssh:/root/.ssh
    steps:
      - name: Run yocto distro build
        run: |
          apt update
          DEBIAN_FRONTEND=noninteractive apt install -y --no-install-recommends build-essential chrpath cpio curl \
            diffstat file gawk gcc-multilib git-core jq libegl1-mesa libffi-dev liblz4-tool \
            libsdl1.2-dev libssl-dev locales lz4 mesa-common-dev openssh-client python3 \
            python3-dev python3-git python3-jinja2 python3-pexpect python3-pip python3 subunit \
            python3-venv socat texinfo unzip wget xterm xxd xz-utils zstd rsync
          locale-gen en_US.UTF-8 && update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
          git config --global --add safe.directory '*'

      - name: Checkout repo and its submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          set-safe-directory: '*'
          token: ${{ secrets.ACCESS_REPO_TOKEN }}

      - name: Run yocto distro build
        run: |
          export HOME=/root
          git config --global --add safe.directory '*'
          # because gh runs sh instead of bash we have to use . instead of "source" and set variable
          set -- build-${{ inputs.arch }}
          TEMPLATECONF=meta-ssrc-ros2/conf/${{ inputs.arch }}-template . ./oe-init-build-env
          # SDK fails to build when using debian packages. RPM must to be used instead.
          echo "PACKAGE_CLASSES = \"package_rpm\"" >> conf/local.conf
          bitbake -c populate_sdk ${{ env.IMAGE_RECIPE_NAME }}
          ls tmp-glibc/deploy/sdk/

      - name: Upload to upmknas
        env:
          ARCHITECTURE: ${{ inputs.arch }}
          NAS_ADDRESS: upmknas
          NAS_USER: devops
          NAS_SHARE_PATH: /share/drone-yocto-sstate-cache
        run: |
          set -exu
          workdir=$(pwd)

          NAS_DESTINATION_PATH=${{ env.NAS_SHARE_PATH }}/yocto/sdk/${{ env.ARCHITECTURE }}
          # Upload to "<user>@<upmknas>/share/drone-yocto-sstate-cache/yocto/sdk/<arch>/<github_sha>" dir
          dest_url="${{ env.NAS_USER }}@${{ env.NAS_ADDRESS }}:${NAS_DESTINATION_PATH}/${GITHUB_SHA}/"

          if [ -d "build-${{ env.ARCHITECTURE }}/tmp-glibc/deploy/sdk" ]; then
            cd build-${{ env.ARCHITECTURE }}/tmp-glibc/deploy/sdk > /dev/null
            rsync -avz --progress * ${dest_url}
          fi
