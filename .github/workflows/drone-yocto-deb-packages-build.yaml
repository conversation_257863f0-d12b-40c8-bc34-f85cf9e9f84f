name: drone-yocto-deb-packages-build

on:
  workflow_dispatch:
    inputs:
      publish_in_digitalocean:
        description: 'Publish debian repo in DigitalOcean'
        required: true
        default: true
        type: boolean
  schedule:
    # Runs "At 21:00 on every Friday"
    - cron: '0 21 * * 5'

env:
  REPO_NAME: drone-yocto
  IMAGE_RECIPE_NAME: fog-deb-package-build-image

jobs:
  variables:
    runs-on: ubuntu-latest
    outputs:
      PUBLISH_IN_DIGITALOCEAN: ${{ steps.set_variables.outputs.PUBLISH_IN_DIGITALOCEAN }}
    steps:
      - name: set variables
        id: set_variables
        run: |
          # set default value for PUBLISH_IN_DIGITALOCEAN to be true
          # if set as input parameter, use that value to override default
          PUBLISH_IN_DIGITALOCEAN=true
          if [ -n "${{ github.event.inputs.publish_in_digitalocean }}" ]; then
            PUBLISH_IN_DIGITALOCEAN=${{ github.event.inputs.publish_in_digitalocean }}
          fi

          echo "PUBLISH_IN_DIGITALOCEAN=$PUBLISH_IN_DIGITALOCEAN" >> $GITHUB_OUTPUT
  build:
    needs: variables
    strategy:
      fail-fast: false
      matrix:
        arch: [amd64, arm64, riscv64]
    uses: ./.github/workflows/drone-yocto-deb-packages-build-common.yaml
    with:
      arch: ${{ matrix.arch }}
      publish_in_digitalocean: ${{ needs.variables.outputs.PUBLISH_IN_DIGITALOCEAN == 'true' }}
    secrets: inherit
  index_deb_packages:
    if: ${{ needs.variables.outputs.PUBLISH_IN_DIGITALOCEAN == 'true' }}
    runs-on: dlabbuilder-yocto-deb-indexing
    container:
      image: ubuntu:22.04
      volumes:
        - /home/<USER>/.ssh:/root/.ssh
    needs:
      - variables
      - build
    env:
      # upmknas ip address as tailscale wont work inside container
      ADDRESS: devops@*************
      INBOX: /share/ssdpmk/drone/drone-yocto/inbox/
      DISTRIBUTION: humble
      COMPONENT: ${GITHUB_REF##*/}
      WORKDIR: debian_repo_compose
      APTLY_REPO_NAME: aptly-$GITHUB_SHA
      SNAPSHOT_NAME: snapshot-$GITHUB_SHA
      AWS_ACCESS_KEY_ID: ${{ secrets.CONFIG_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.CONFIG_SECRET_ACCESS_KEY }}
    defaults:
      run:
        shell: bash

    steps:
      - name: checkout
        uses: actions/checkout@v4
      - name: configuration
        run: |
          set -exu

          # aptly uses configuration from home directory, it could be chaged with parameter but it
          # should be passed to every aptly call so it is easier to just copy it to home directory
          cp .aptly.conf ~/.aptly.conf
          apt update
          DEBIAN_FRONTEND=noninteractive apt install -y --no-install-recommends aptly ca-certificates openssh-client rsync jq
      - name: download deb packages from NAS
        run: |
          set -exu

          # download deb packages from NAS to WORKDIR
          rsync -a ${{ env.ADDRESS }}:${{ env.INBOX }}/drone-yocto/${{ env.DISTRIBUTION }}/${{ env.COMPONENT }} ${{ env.WORKDIR }}
      - name: create debian package index
        if: ${{ needs.variables.outputs.PUBLISH_IN_DIGITALOCEAN == 'true'  }}
        run: |
          set -exu

          # create aptly repo and add deb packages
          aptly repo create -distribution=${{ env.DISTRIBUTION}} -component=${{ env.COMPONENT }} ${{ env.APTLY_REPO_NAME }}
          aptly repo add ${{ env.APTLY_REPO_NAME}} ${{ env.WORKDIR}}

          # create snapshot and publish it
          PUBLISH_OUTPUT=publish_output.txt
          aptly snapshot create ${{ env.SNAPSHOT_NAME }} from repo ${{ env.APTLY_REPO_NAME}}

          # running tee masks exit code of aptly publish command so we have to use subshell with pipefail
          (
            set -o pipefail
            # publish snapshot with -force-overwrite to allow republishing
            # otherwise you are only able to compile each branch once
            aptly publish snapshot -skip-signing -force-overwrite -architectures=amd64,arm64,riscv64 ${{ env.SNAPSHOT_NAME }} s3:DigitalOcean:${{ env.COMPONENT }} | tee ${PUBLISH_OUTPUT}
          )

          # generate publish summary
          bucket_name=$(jq -r .S3PublishEndpoints.DigitalOcean.bucket .aptly.conf)
          endpoint_name=$(jq -r .S3PublishEndpoints.DigitalOcean.endpoint .aptly.conf)
          server_addr=${bucket_name}.${endpoint_name}
          echo "### debian repo published" >> $GITHUB_STEP_SUMMARY
          grep 'Now you can add' -A1 ${PUBLISH_OUTPUT} |sed "s/your-server/${server_addr}/" >> $GITHUB_STEP_SUMMARY
