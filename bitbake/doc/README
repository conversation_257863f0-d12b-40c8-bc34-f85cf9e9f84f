Documentation
=============

This is the directory that contains the BitBake documentation. 

Manual Organization
===================

Folders exist for individual manuals as follows:

* bitbake-user-manual      --- The BitBake User Manual 

Each folder is self-contained regarding content and figures.

If you want to find HTML versions of the BitBake manuals on the web, 
go to https://www.openembedded.org/wiki/Documentation.

Sphinx
======

The BitBake documentation was migrated from the original DocBook
format to Sphinx based documentation for the Yocto Project 3.2
release.

Additional information related to the Sphinx migration, and guidelines
for developers willing to contribute to the BitBake documentation can
be found in the Yocto Project Documentation README file:

https://git.yoctoproject.org/cgit/cgit.cgi/yocto-docs/tree/documentation/README

How to build the Yocto Project documentation
============================================

Sphinx is written in Python. While it might work with Python2, for
obvious reasons, we will only support building the BitBake
documentation with Python3.

Sphinx might be available in your Linux distro packages repositories,
however it is not recommend using distro packages, as they might be
old versions, especially if you are using an LTS version of your
distro. The recommended method to install Sphinx and all required
dependencies is to use the Python Package Index (pip).

To install all required packages run:

 $ pip3 install sphinx sphinx_rtd_theme pyyaml

To build the documentation locally, run:

 $ cd documentation
 $ make -f Makefile.sphinx html

The resulting HTML index page will be _build/html/index.html, and you
can browse your own copy of the locally generated documentation with
your browser.
