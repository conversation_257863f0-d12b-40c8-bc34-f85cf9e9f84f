BitBake is licensed under the GNU General Public License version 2.0. See 
LICENSE.GPL-2.0-only for further details.

Individual files contain the following style tags instead of the full license text:

    SPDX-License-Identifier:	GPL-2.0-only

This enables machine processing of license information based on the SPDX
License Identifiers that are here available: http://spdx.org/licenses/


The following external components are distributed with this software:

* The Toaster Simple UI application is based upon the Django project template, the files of which are covered by the BSD license and are copyright (c) Django Software
Foundation and individual contributors.

* Twitter Bootstrap (including Glyphicons), redistributed under the MIT license
* jQuery is redistributed under the MIT license.

* Twitter typeahead.js redistributed under the MIT license. Note that the JS source has one small modification, so the full unminified file is currently included to make it obvious where this is.

* jsrender is redistributed under the MIT license.

* QUnit is redistributed under the MIT license.

* Font Awesome fonts redistributed under the SIL Open Font License 1.1

* simplediff is distributed under the zlib license.

