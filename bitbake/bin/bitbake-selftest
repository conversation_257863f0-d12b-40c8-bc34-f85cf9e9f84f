#!/usr/bin/env python3
#
# Copyright (C) 2012 <PERSON>
#
# SPDX-License-Identifier: GPL-2.0-only
#

import os
import sys, logging
import warnings
warnings.simplefilter("default")
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib'))

import unittest
try:
    import bb
    import hashserv
    import layerindexlib
except RuntimeError as exc:
    sys.exit(str(exc))

tests = ["bb.tests.codeparser",
         "bb.tests.color",
         "bb.tests.cooker",
         "bb.tests.cow",
         "bb.tests.data",
         "bb.tests.event",
         "bb.tests.fetch",
         "bb.tests.parse",
         "bb.tests.persist_data",
         "bb.tests.runqueue",
         "bb.tests.siggen",
         "bb.tests.utils",
         "bb.tests.compression",
         "hashserv.tests",
         "layerindexlib.tests.layerindexobj",
         "layerindexlib.tests.restapi",
         "layerindexlib.tests.cooker"]

for t in tests:
    t = '.'.join(t.split('.')[:3])
    __import__(t)


# Set-up logging
class StdoutStreamHandler(logging.StreamHandler):
    """Special handler so that unittest is able to capture stdout"""
    def __init__(self):
        # Override __init__() because we don't want to set self.stream here
        logging.Handler.__init__(self)

    @property
    def stream(self):
        # We want to dynamically write wherever sys.stdout is pointing to
        return sys.stdout


handler = StdoutStreamHandler()
bb.logger.addHandler(handler)
bb.logger.setLevel(logging.DEBUG)


ENV_HELP = """\
Environment variables:
  BB_SKIP_NETTESTS      set to 'yes' in order to skip tests using network
                        connection
  BB_TMPDIR_NOCLEAN     set to 'yes' to preserve test tmp directories
"""

class main(unittest.main):
    def _print_help(self, *args, **kwargs):
        super(main, self)._print_help(*args, **kwargs)
        print(ENV_HELP)


if __name__ == '__main__':
        main(defaultTest=tests, buffer=True)
