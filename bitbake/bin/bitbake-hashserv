#! /usr/bin/env python3
#
# Copyright (C) 2018 Garmin Ltd.
#
# SPDX-License-Identifier: GPL-2.0-only
#

import os
import sys
import logging
import argparse
import sqlite3
import warnings
warnings.simplefilter("default")

sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib'))

import hashserv

VERSION = "1.0.0"

DEFAULT_BIND = 'unix://./hashserve.sock'


def main():
    parser = argparse.ArgumentParser(description='Hash Equivalence Reference Server. Version=%s' % VERSION,
                                     epilog='''The bind address is the path to a unix domain socket if it is
                                               prefixed with "unix://". Otherwise, it is an IP address
                                               and port in form ADDRESS:PORT. To bind to all addresses, leave
                                               the ADDRESS empty, e.g. "--bind :8686". To bind to a specific
                                               IPv6 address, enclose the address in "[]", e.g.
                                               "--bind [::1]:8686"'''
                                     )

    parser.add_argument('-b', '--bind', default=DEFAULT_BIND, help='Bind address (default "%(default)s")')
    parser.add_argument('-d', '--database', default='./hashserv.db', help='Database file (default "%(default)s")')
    parser.add_argument('-l', '--log', default='WARNING', help='Set logging level')
    parser.add_argument('-u', '--upstream', help='Upstream hashserv to pull hashes from')
    parser.add_argument('-r', '--read-only', action='store_true', help='Disallow write operations from clients')

    args = parser.parse_args()

    logger = logging.getLogger('hashserv')

    level = getattr(logging, args.log.upper(), None)
    if not isinstance(level, int):
        raise ValueError('Invalid log level: %s' % args.log)

    logger.setLevel(level)
    console = logging.StreamHandler()
    console.setLevel(level)
    logger.addHandler(console)

    server = hashserv.create_server(args.bind, args.database, upstream=args.upstream, read_only=args.read_only)
    server.serve_forever()
    return 0


if __name__ == '__main__':
    try:
        ret = main()
    except Exception:
        ret = 1
        import traceback
        traceback.print_exc()
    sys.exit(ret)
