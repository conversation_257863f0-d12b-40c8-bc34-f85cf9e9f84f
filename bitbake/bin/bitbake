#!/usr/bin/env python3
#
# Copyright (C) 2003, 2004  <PERSON>
# Copyright (C) 2003, 2004  <PERSON>
# Copyright (C) 2003 - 2005 <PERSON>
# Copyright (C) 2005        Holge<PERSON> <PERSON>
# Copyright (C) 2005        ROAD GmbH
# Copyright (C) 2006        <PERSON>
#
# SPDX-License-Identifier: GPL-2.0-only
#

import os
import sys
import warnings
warnings.simplefilter("default")

sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                'lib'))
try:
    import bb
except RuntimeError as exc:
    sys.exit(str(exc))

from bb import cookerdata
from bb.main import bitbake_main, BitBakeConfigParameters, BBMainException

if sys.getfilesystemencoding() != "utf-8":
    sys.exit("Please use a locale setting which supports UTF-8 (such as LANG=en_US.UTF-8).\nPython can't change the filesystem locale after loading so we need a UTF-8 when Python starts or things won't work.")

__version__ = "2.0.0"

if __name__ == "__main__":
    if __version__ != bb.__version__:
        sys.exit("Bitbake core version and program version mismatch!")
    try:
        sys.exit(bitbake_main(BitBakeConfigParameters(sys.argv),
                              cookerdata.CookerConfiguration()))
    except BBMainException as err:
        sys.exit(err)
    except bb.BBHandledException:
        sys.exit(1)
    except Exception:
        import traceback
        traceback.print_exc()
        sys.exit(1)
