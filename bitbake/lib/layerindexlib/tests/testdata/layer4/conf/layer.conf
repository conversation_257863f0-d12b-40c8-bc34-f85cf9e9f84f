# We have a conf and classes directory, append to BBPATH
BBPATH .= ":${LAYERDIR}"

# We have a recipes directory, add to BBFILES
BBFILES += "${LAYERDIR}/recipes-*/*/*.bb ${LAYERDIR}/recipes-*/*/*.bbappend"

BBFILE_COLLECTIONS += "openembedded-layer"
BBFILE_PATTERN_openembedded-layer := "^${LAYERDIR}/"

# Define the priority for recipes (.bb files) from this layer,
# choosing carefully how this layer interacts with all of the
# other layers.

BBFILE_PRIORITY_openembedded-layer = "6"

# This should only be incremented on significant changes that will
# cause compatibility issues with other layers
LAYERVERSION_openembedded-layer = "1"

LAYERDEPENDS_openembedded-layer = "core"

LAYERSERIES_COMPAT_openembedded-layer = "sumo"
