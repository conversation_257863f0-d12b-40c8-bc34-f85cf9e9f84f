# We have a conf and classes directory, add to BBPATH
BBPATH .= ":${LAYERDIR}"

# We have a packages directory, add to BBFILES
BBFILES += "${LAYERDIR}/recipes-*/*/*.bb \
            ${LAYERDIR}/recipes-*/*/*.bbappend"

BBFILE_COLLECTIONS += "networking-layer"
BBFILE_PATTERN_networking-layer := "^${LAYERDIR}/"
BBFILE_PRIORITY_networking-layer = "5"

# This should only be incremented on significant changes that will
# cause compatibility issues with other layers
LAYERVERSION_networking-layer = "1"

LAYERDEPENDS_networking-layer = "core"
LAYERDEPENDS_networking-layer += "openembedded-layer"
LAYERDEPENDS_networking-layer += "meta-python"

LAYERSERIES_COMPAT_networking-layer = "sumo"
