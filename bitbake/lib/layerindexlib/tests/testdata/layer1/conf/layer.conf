# We have a conf and classes directory, add to BBPATH
BBPATH .= ":${LAYERDIR}"
# We have recipes-* directories, add to BBFILES
BBFILES += "${LAYERDIR}/recipes-*/*/*.bb"

BBFILE_COLLECTIONS += "core"
BBFILE_PATTERN_core = "^${LAYERDIR}/"
BBFILE_PRIORITY_core = "5"

LAYERSERIES_CORENAMES = "sumo"

# This should only be incremented on significant changes that will
# cause compatibility issues with other layers
LAYERVERSION_core = "11"
LAYERSERIES_COMPAT_core = "sumo"

BBLAYERS_LAYERINDEX_NAME_core = "openembedded-core"
