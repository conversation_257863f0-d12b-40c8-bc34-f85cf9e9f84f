# We might have a conf and classes directory, append to BBPATH
BBPATH .= ":${LAYERDIR}"

# We have recipes directories, add to BBFILES
BBFILES += "${LAYERDIR}/recipes*/*/*.bb ${LAYERDIR}/recipes*/*/*.bbappend"

BBFILE_COLLECTIONS += "meta-python"
BBFILE_PATTERN_meta-python := "^${LAYERDIR}/"
BBFILE_PRIORITY_meta-python = "7"

# This should only be incremented on significant changes that will
# cause compatibility issues with other layers
LAYERVERSION_meta-python = "1"

LAYERDEPENDS_meta-python = "core openembedded-layer"

LAYERSERIES_COMPAT_meta-python = "sumo"

LICENSE_PATH += "${LAYERDIR}/licenses"
