<?xml version="1.0" encoding="utf-8"?>
<django-objects version="1.0">
  <!-- Set the project default value for DISTRO -->
  <object model="orm.toastersetting" pk="1">
    <field type="CharField" name="name">DEFCONF_DISTRO</field>
    <field type="CharField" name="value">poky</field>
  </object>

  <!-- Bitbake versions which correspond to the metadata release -->
  <object model="orm.bitbakeversion" pk="1">
    <field type="CharField" name="name">kirkstone</field>
    <field type="CharField" name="giturl">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="branch">kirkstone</field>
    <field type="CharField" name="dirpath">bitbake</field>
  </object>
  <object model="orm.bitbakeversion" pk="2">
    <field type="CharField" name="name">HEAD</field>
    <field type="CharField" name="giturl">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="branch">HEAD</field>
    <field type="CharField" name="dirpath">bitbake</field>
  </object>
  <object model="orm.bitbakeversion" pk="3">
    <field type="CharField" name="name">master</field>
    <field type="CharField" name="giturl">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="branch">master</field>
    <field type="CharField" name="dirpath">bitbake</field>
  </object>
  <object model="orm.bitbakeversion" pk="4">
    <field type="CharField" name="name">honister</field>
    <field type="CharField" name="giturl">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="branch">honister</field>
    <field type="CharField" name="dirpath">bitbake</field>
  </object>
  <object model="orm.bitbakeversion" pk="5">
    <field type="CharField" name="name">hardknott</field>
    <field type="CharField" name="giturl">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="branch">hardknott</field>
    <field type="CharField" name="dirpath">bitbake</field>
  </object>


  <!-- Releases available -->
  <object model="orm.release" pk="1">
    <field type="CharField" name="name">kirkstone</field>
    <field type="CharField" name="description">Yocto Project 3.5 "Kirkstone"</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">1</field>
    <field type="CharField" name="branch_name">kirkstone</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href="https://git.yoctoproject.org/cgit/cgit.cgi/poky/log/?h=kirkstone"&gt;Yocto Project Kirkstone branch&lt;/a&gt;.</field>
  </object>
  <object model="orm.release" pk="2">
    <field type="CharField" name="name">local</field>
    <field type="CharField" name="description">Local Yocto Project</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">2</field>
    <field type="CharField" name="branch_name">HEAD</field>
    <field type="TextField" name="helptext">Toaster will run your builds with the version of the Yocto Project you have cloned or downloaded to your computer.</field>
  </object>
  <object model="orm.release" pk="3">
    <field type="CharField" name="name">master</field>
    <field type="CharField" name="description">Yocto Project master</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">3</field>
    <field type="CharField" name="branch_name">master</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href="https://git.yoctoproject.org/cgit/cgit.cgi/poky/log/"&gt;Yocto Project Master branch&lt;/a&gt;.</field>
  </object>
  <object model="orm.release" pk="4">
    <field type="CharField" name="name">honister</field>
    <field type="CharField" name="description">Yocto Project 3.4 "Honister"</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">4</field>
    <field type="CharField" name="branch_name">honister</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href="https://git.yoctoproject.org/cgit/cgit.cgi/poky/log/?h=honister"&gt;Yocto Project Honister branch&lt;/a&gt;.</field>
  </object>
  <object model="orm.release" pk="5">
    <field type="CharField" name="name">hardknott</field>
    <field type="CharField" name="description">Yocto Project 3.3 "Hardknott"</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">5</field>
    <field type="CharField" name="branch_name">hardknott</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href="https://git.yoctoproject.org/cgit/cgit.cgi/poky/log/?h=hardknott"&gt;Yocto Project Hardknott branch&lt;/a&gt;.</field>
  </object>

  <!-- Default project layers for each release -->
  <object model="orm.releasedefaultlayer" pk="1">
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="2">
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="layer_name">meta-poky</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="3">
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="layer_name">meta-yocto-bsp</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="4">
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="5">
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="layer_name">meta-poky</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="6">
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="layer_name">meta-yocto-bsp</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="7">
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="8">
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="layer_name">meta-poky</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="9">
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="layer_name">meta-yocto-bsp</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="10">
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="11">
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="layer_name">meta-poky</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="12">
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="layer_name">meta-yocto-bsp</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="13">
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="14">
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="layer_name">meta-poky</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="15">
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="layer_name">meta-yocto-bsp</field>
  </object>

  <!-- Default layers provided by poky
       openembedded-core
       meta-poky
       meta-yocto-bsp
  -->
  <object model="orm.layer" pk="1">
    <field type="CharField" name="name">openembedded-core</field>
    <field type="CharField" name="layer_index_url"></field>
    <field type="CharField" name="vcs_url">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="vcs_web_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky</field>
    <field type="CharField" name="vcs_web_tree_base_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky/tree/%path%?h=%branch%</field>
    <field type="CharField" name="vcs_web_file_base_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky/tree/%path%?h=%branch%</field>
  </object>
  <object model="orm.layer_version" pk="1">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">1</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="branch">kirkstone</field>
    <field type="CharField" name="dirpath">meta</field>
  </object>
  <object model="orm.layer_version" pk="2">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">1</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="branch">HEAD</field>
    <field type="CharField" name="commit">HEAD</field>
    <field type="CharField" name="dirpath">meta</field>
  </object>
  <object model="orm.layer_version" pk="3">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">1</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="branch">master</field>
    <field type="CharField" name="dirpath">meta</field>
  </object>
  <object model="orm.layer_version" pk="4">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">1</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="branch">honister</field>
    <field type="CharField" name="dirpath">meta</field>
  </object>
  <object model="orm.layer_version" pk="5">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">1</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="branch">hardknott</field>
    <field type="CharField" name="dirpath">meta</field>
  </object>

  <object model="orm.layer" pk="2">
    <field type="CharField" name="name">meta-poky</field>
    <field type="CharField" name="layer_index_url"></field>
    <field type="CharField" name="vcs_url">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="vcs_web_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky</field>
    <field type="CharField" name="vcs_web_tree_base_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky/tree/%path%?h=%branch%</field>
    <field type="CharField" name="vcs_web_file_base_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky/tree/%path%?h=%branch%</field>
  </object>
  <object model="orm.layer_version" pk="6">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">2</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="branch">kirkstone</field>
    <field type="CharField" name="dirpath">meta-poky</field>
  </object>
  <object model="orm.layer_version" pk="7">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">2</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="branch">HEAD</field>
    <field type="CharField" name="commit">HEAD</field>
    <field type="CharField" name="dirpath">meta-poky</field>
  </object>
  <object model="orm.layer_version" pk="8">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">2</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="branch">master</field>
    <field type="CharField" name="dirpath">meta-poky</field>
  </object>
  <object model="orm.layer_version" pk="9">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">2</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="branch">honister</field>
    <field type="CharField" name="dirpath">meta-poky</field>
  </object>
  <object model="orm.layer_version" pk="10">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">2</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="branch">hardknott</field>
    <field type="CharField" name="dirpath">meta-poky</field>
  </object>

  <object model="orm.layer" pk="3">
    <field type="CharField" name="name">meta-yocto-bsp</field>
    <field type="CharField" name="layer_index_url"></field>
    <field type="CharField" name="vcs_url">git://git.yoctoproject.org/poky</field>
    <field type="CharField" name="vcs_web_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky</field>
    <field type="CharField" name="vcs_web_tree_base_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky/tree/%path%?h=%branch%</field>
    <field type="CharField" name="vcs_web_file_base_url">https://git.yoctoproject.org/cgit/cgit.cgi/poky/tree/%path%?h=%branch%</field>
  </object>
  <object model="orm.layer_version" pk="11">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">3</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="branch">kirkstone</field>
    <field type="CharField" name="dirpath">meta-yocto-bsp</field>
  </object>
  <object model="orm.layer_version" pk="12">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">3</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="branch">HEAD</field>
    <field type="CharField" name="commit">HEAD</field>
    <field type="CharField" name="dirpath">meta-yocto-bsp</field>
  </object>
  <object model="orm.layer_version" pk="13">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">3</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="branch">master</field>
    <field type="CharField" name="dirpath">meta-yocto-bsp</field>
  </object>
  <object model="orm.layer_version" pk="14">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">3</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="branch">honister</field>
    <field type="CharField" name="dirpath">meta-yocto-bsp</field>
  </object>
  <object model="orm.layer_version" pk="15">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">3</field>
    <field type="IntegerField" name="layer_source">0</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="branch">hardknott</field>
    <field type="CharField" name="dirpath">meta-yocto-bsp</field>
  </object>
</django-objects>
