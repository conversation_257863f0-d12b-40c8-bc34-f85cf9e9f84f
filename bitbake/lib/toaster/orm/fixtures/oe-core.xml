<?xml version="1.0" encoding="utf-8"?>
<django-objects version="1.0">
  <!-- Set the project default value for DISTRO -->
  <object model="orm.toastersetting" pk="1">
    <field type="CharField" name="name">DEFCONF_DISTRO</field>
    <field type="CharField" name="value">nodistro</field>
  </object>

  <!-- Bitbake versions which correspond to the metadata release -->
  <object model="orm.bitbakeversion" pk="1">
    <field type="CharField" name="name">kirkstone</field>
    <field type="CharField" name="giturl">git://git.openembedded.org/bitbake</field>
    <field type="CharField" name="branch">1.54</field>
  </object>
  <object model="orm.bitbakeversion" pk="2">
    <field type="CharField" name="name">HEAD</field>
    <field type="CharField" name="giturl">git://git.openembedded.org/bitbake</field>
    <field type="CharField" name="branch">HEAD</field>
  </object>
  <object model="orm.bitbakeversion" pk="3">
    <field type="CharField" name="name">master</field>
    <field type="CharField" name="giturl">git://git.openembedded.org/bitbake</field>
    <field type="CharField" name="branch">master</field>
  </object>
  <object model="orm.bitbakeversion" pk="4">
    <field type="CharField" name="name">honister</field>
    <field type="CharField" name="giturl">git://git.openembedded.org/bitbake</field>
    <field type="CharField" name="branch">1.52</field>
  </object>
  <object model="orm.bitbakeversion" pk="5">
    <field type="CharField" name="name">hardknott</field>
    <field type="CharField" name="giturl">git://git.openembedded.org/bitbake</field>
    <field type="CharField" name="branch">1.50</field>
  </object>

  <!-- Releases available -->
  <object model="orm.release" pk="1">
    <field type="CharField" name="name">kirkstone</field>
    <field type="CharField" name="description">Openembedded Kirkstone</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">1</field>
    <field type="CharField" name="branch_name">kirkstone</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href=\"https://cgit.openembedded.org/openembedded-core/log/?h=kirkstone\"&gt;OpenEmbedded Kirkstone&lt;/a&gt; branch.</field>
  </object>
  <object model="orm.release" pk="2">
    <field type="CharField" name="name">local</field>
    <field type="CharField" name="description">Local Openembedded</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">2</field>
    <field type="CharField" name="branch_name">HEAD</field>
    <field type="TextField" name="helptext">Toaster will run your builds with the version of OpenEmbedded that you have cloned or downloaded to your computer.</field>
  </object>
  <object model="orm.release" pk="3">
    <field type="CharField" name="name">master</field>
    <field type="CharField" name="description">OpenEmbedded core master</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">3</field>
    <field type="CharField" name="branch_name">master</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href=\"https://cgit.openembedded.org/openembedded-core/log/\"&gt;OpenEmbedded master&lt;/a&gt; branch.</field>
  </object>
  <object model="orm.release" pk="4">
    <field type="CharField" name="name">honister</field>
    <field type="CharField" name="description">Openembedded Honister</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">4</field>
    <field type="CharField" name="branch_name">honister</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href=\"https://cgit.openembedded.org/openembedded-core/log/?h=honister\"&gt;OpenEmbedded Honister&lt;/a&gt; branch.</field>
  </object>
  <object model="orm.release" pk="5">
    <field type="CharField" name="name">hardknott</field>
    <field type="CharField" name="description">Openembedded Hardknott</field>
    <field rel="ManyToOneRel" to="orm.bitbakeversion" name="bitbake_version">5</field>
    <field type="CharField" name="branch_name">hardknott</field>
    <field type="TextField" name="helptext">Toaster will run your builds using the tip of the &lt;a href=\"https://cgit.openembedded.org/openembedded-core/log/?h=hardknott\"&gt;OpenEmbedded Hardknott&lt;/a&gt; branch.</field>
  </object>

  <!-- Default layers for each release -->
  <object model="orm.releasedefaultlayer" pk="1">
    <field rel="ManyToOneRel" to="orm.release" name="release">1</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="2">
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="3">
    <field rel="ManyToOneRel" to="orm.release" name="release">3</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="4">
    <field rel="ManyToOneRel" to="orm.release" name="release">4</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>
  <object model="orm.releasedefaultlayer" pk="5">
    <field rel="ManyToOneRel" to="orm.release" name="release">5</field>
    <field type="CharField" name="layer_name">openembedded-core</field>
  </object>


  <!-- Layer for the Local release -->
  <object model="orm.layer" pk="1">
    <field type="CharField" name="name">openembedded-core</field>
    <field type="CharField" name="vcs_url">git://git.openembedded.org/openembedded-core</field>
    <field type="CharField" name="vcs_web_url">https://cgit.openembedded.org/openembedded-core</field>
    <field type="CharField" name="vcs_web_tree_base_url">https://cgit.openembedded.org/openembedded-core/tree/%path%?h=%branch%</field>
    <field type="CharField" name="vcs_web_file_base_url">https://cgit.openembedded.org/openembedded-core/tree/%path%?h=%branch%</field>
  </object>
  <object model="orm.layer_version" pk="1">
    <field rel="ManyToOneRel" to="orm.layer" name="layer">1</field>
    <field rel="ManyToOneRel" to="orm.release" name="release">2</field>
    <field type="CharField" name="local_path">OE-CORE-LAYER-DIR</field>
    <field type="CharField" name="branch">HEAD</field>
    <field type="CharField" name="dirpath">meta</field>
    <field type="IntegerField" name="layer_source">0</field>
  </object>

</django-objects>
