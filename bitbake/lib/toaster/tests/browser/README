# Running Toaster's browser-based test suite

These tests require Selenium to be installed in your Python environment.

The simplest way to install this is via pip3:

  pip3 install selenium==2.53.2

Note that if you use other versions of Selenium, some of the tests (such as
tests.browser.test_js_unit_tests.TestJsUnitTests) may fail, as these rely on
a Selenium test report with a version-specific format.

To run tests against Chrome:

* Download chromedriver for your host OS from
  https://sites.google.com/a/chromium.org/chromedriver/downloads
* On *nix systems, put chromedriver on PATH
* On Windows, put chromedriver.exe in the same directory as chrome.exe

To run tests against PhantomJS (headless):
--NOTE - Selenium seems to be deprecating support for this mode ---
* Download and install PhantomJS:
  http://phantomjs.org/download.html
* On *nix systems, put phantomjs on PATH
* Not tested on Windows

To run tests against Firefox, you may need to install the Marionette driver,
depending on how new your version of Firefox is. One clue that you need to do
this is if you see an exception like:

  selenium.common.exceptions.WebDriverException: Message: The browser
  appears to have exited before we could connect. If you specified
  a log_file in the FirefoxBinary constructor, check it for details.

See https://developer.mozilla.org/en-US/docs/Mozilla/QA/Marionette/WebDriver
for installation instructions. Ensure that the Marionette executable (renamed
as wires on Linux or wires.exe on Windows) is on your PATH; and use "marionette"
as the browser string passed via TOASTER_TESTS_BROWSER (see below).

(Note: The Toaster tests have been checked against Firefox 47 with the
Marionette driver.)

The test cases will instantiate a Selenium driver set by the
TOASTER_TESTS_BROWSER environment variable, or Chrome if this is not specified.

To run tests against the Selenium Firefox Docker container:
More explanation is located at https://wiki.yoctoproject.org/wiki/TipsAndTricks/TestingToasterWithContainers
* Run the Selenium container:
  ** docker run -it --rm=true -p 5900:5900 -p 4444:4444 --name=selenium selenium/standalone-firefox-debug:2.53.0
  *** 5900 is the default vnc port. If you are runing a vnc server on your machine map a different port e.g. -p 6900:5900 and connect vnc client to 127.0.0.1:6900
  *** 4444 is the default selenium sever port.
* Run the tests
  ** TOASTER_TESTS_BROWSER=http://127.0.0.1:4444/wd/hub TOASTER_TESTS_URL=http://**********:8000 ./bitbake/lib/toaster/manage.py test --liveserver=**********:8000 tests.browser
  ** TOASTER_TESTS_BROWSER=remote TOASTER_REMOTE_HUB=http://127.0.0.1:4444/wd/hub ./bitbake/lib/toaster/manage.py test --liveserver=**********:8000 tests.browser
  *** TOASTER_REMOTE_HUB - This is the address for the Selenium Remote Web Driver hub. Assuming you ran the contianer with -p 4444:4444 it will be http://127.0.0.1:4444/wd/hub.
  ***  --liveserver=xxx tells Django to run the test server on an interface and port reachable by both host and container.
  **** ********** is the default docker bridge on linux, viewable from inside and outside the contianers. Find it with "ip -4 addr show dev docker0"
* connect to the vnc server to see the tests if you would like
   ** xtightvncviewer 127.0.0.1:5900
   ** note, you need to wait for the test container to come up before this can connect.

Available drivers:

* chrome (default)
* firefox
* marionette (for newer Firefoxes)
* ie
* phantomjs (deprecated)
* remote

e.g. to run the test suite with phantomjs where you have phantomjs installed
in /home/<USER>/apps/phantomjs:

PATH=/home/<USER>/apps/phantomjs/bin:$PATH TOASTER_TESTS_BROWSER=phantomjs manage.py test tests.browser
