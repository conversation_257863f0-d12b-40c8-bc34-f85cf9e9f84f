<a href="{% url 'project' extra.pid %}?setMachine={{data.name}}" class="btn btn-default btn-block layer-exists-{{data.layer_version.id}}"
    {% if data.layer_version.pk not in extra.current_layers %}
    style="display:none;"
    {% endif %}
>
  Select machine</a>
<a class="btn btn-default btn-block layerbtn
layer-add-{{data.layer_version.id}}" data-layer='{ "id":
   {{data.layer_version.id}}, "name":  "{{data.layer_version.layer.name}}",
   "layerdetailurl": "{%url 'layerdetails' extra.pid data.layer_version.id %}",
   "xhrLayerUrl": "{% url "xhr_layer" extra.pid data.layer_version.id %}"
   }' data-directive="add"
    {% if data.layer_version.pk in extra.current_layers %}
    style="display:none;"
    {% endif %}
>
  <span class="glyphicon glyphicon-plus"></span>
  Add layer
  <span class="glyphicon glyphicon-question-sign get-help" title="To select this machine, you must first add the {{data.layer_version.layer.name}} layer to your project"></i>
</a>

