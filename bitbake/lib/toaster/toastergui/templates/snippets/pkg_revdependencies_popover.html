{# Popover that displays the reverse dependences and sizes of a package 'data' used in the Packages table #}
{% load projecttags %}

{% with package_deps=data.package_dependencies_target|for_target:extra.target_name %}
{% with count_package=package_deps.packages|length %}

{% if count_package > 0 %}
  <a data-content='<ul class="list-unstyled">
  {% for dep in package_deps.packages|dictsort:"package.name" %}
     <li>
      {% if extra.add_links %}
      <a href="{% url 'package_included_detail' extra.build.pk extra.target_id dep.package.pk %}">
      {{dep.package.name}}</a>
      {% else %}
      {{dep.package.name}}
      {% endif %}
      {% if dep.package.size > 0 %}
        ({{dep.package.size|filtered_filesizeformat}})
      {% endif %}
     </li>
   {% endfor %}
   </ul>' class="btn btn-default" title='
   <strong>
     {% if extra.add_links %}
      <a href="{% url 'package_included_reverse_dependencies' extra.build.pk extra.target_id data.pk %}">
      {{data.name}}</a>
      {% else %}
      {{data.name}}
      {% endif %}
      </strong>
      dependencies -
      <strong>{{package_deps.size|filtered_filesizeformat}}</strong>'>
      {{count_package}}
  </a>
{% endif %}

{% endwith %}
{% endwith %}
