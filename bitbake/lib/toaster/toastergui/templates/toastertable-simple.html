
{% load static %}
{% load projecttags %}

<script src="{% static 'js/table.js' %}"></script>
<script src="{% static 'js/layerBtn.js' %}"></script>
<script>
  $(document).ready(function() {
    (function(){

    var ctx = {
      tableName : "{{table_name}}",
      url : "{{ xhr_table_url }}?format=json",
      title : "{{title}}",
      projectLayers : {{projectlayers|json}},
    };

    try {
      tableInit(ctx);
    } catch (e) {
      document.write("Problem loading table widget: " + e);
    }
    })();
  });
</script>

{% include 'toastertable-filter.html' %}

<div id="no-results-{{table_name}}" style="display:none">
  <div class="alert alert-warning">
    <form class="no-results form-inline">
      <div class="form-group">
        <div class="btn-group">
          <input class="form-control" id="new-search-input-{{table_name}}" name="search" type="text" placeholder="Search {{title|lower}}" value="{% if request.GET.search %}{{request.GET.search}}{% endif %}"/>
          <span class="remove-search-btn-{{table_name}} glyphicon glyphicon-remove-circle" tabindex="-1"></span>
        </div>
      </div>
      <button class="btn btn-default search-submit-{{table_name}}">Search</button>
      <button class="btn btn-link remove-search-btn-{{table_name}}">Show all {{title|lower}}</button>
    </form>
  </div>
</div>
<div id="table-container-{{table_name}}" style="visibility: hidden">
  <!-- control header -->
  <div id="table-chrome-{{table_name}}">
    <div class="container-fluid detail-page-contols">
      <form class="navbar-form navbar-left">
        <div class="form-group">
          <div class="btn-group">
            <input class="form-control" id="search-input-{{table_name}}" name="search" type="text" placeholder="Search {{title|lower}}" value="{% if request.GET.search %}{{request.GET.search}}{% endif %}"/>
            <span href="#" style="display:none" class="remove-search-btn-{{table_name}} glyphicon glyphicon-remove-circle" tabindex="-1"></span>
          </div>
        </div>
        <button class="btn btn-default" id="search-submit-{{table_name}}" >Search</button>
      </form>

      <form class="navbar-form navbar-right">
        <div class="form-group">
          <label>Show rows:</label>
          <select class="form-control pagesize-{{table_name}}">
            {% with "10 25 50 100 150" as list%}
            {% for i in list.split %}
            <option value="{{i}}">{{i}}</option>
            {% endfor %}
            {% endwith %}
          </select>
        </div>
      </form>
    </div>
  </div>

  <!-- The actual table -->
  <div class="table-responsive">
    <table class="table table-bordered table-hover" id="{{table_name}}">
      <thead>
        <tr><th></th></tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <!-- Pagination controls -->
  <div id="pagination-{{table_name}}">
    <ul class="pagination">
    </ul>

    <form class="navbar-form navbar-right">
      <div class="form-group">
        <label>Show rows:</label>
        <select class="form-control pagesize-{{table_name}}">
          {% with "10 25 50 100 150" as list%}
          {% for i in list.split %}
          <option value="{{i}}">{{i}}</option>
          {% endfor %}
          {% endwith %}
        </select>
      </div>
    </form>
  </div>
</div>
