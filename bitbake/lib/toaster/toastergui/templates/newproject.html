{% extends "base.html" %}
{% load projecttags %}
{% load humanize %}

{% block title %} Create a new project - Toaster {% endblock %}

{% block pagecontent %}
<div class="row">
  <div class="col-md-12">
    <div class="page-header">
          <h1>Create a new project</h1>
        </div>
    {% if alert %}
      <div class="alert alert-danger" role="alert">{{alert}}</div>
    {% endif %}

        <form method="POST">{% csrf_token %}
          <div class="form-group" id="validate-project-name">
            <label class="control-label">Project name <span class="text-muted">(required)</span></label>
            <input type="text" class="form-control" required id="new-project-name" name="projectname">
          </div>
          <p class="help-block text-danger" style="display: none;" id="hint-error-project-name">A project with this name exists. Project names must be unique.</p>

                <label class="project-form">Project type:</label>
                {% if releases.count > 0 %}
                    <label class="project-form radio" style="padding-left: 35px;"><input id='type-new'    type="radio" name="ptype" value="new"/> New project</label>
                {% endif %}
                    <label class="project-form radio" style="padding-left: 35px;"><input id='type-import' type="radio" name="ptype" value="import"/> Import command line project</label>

        {% if releases.count > 0 %}
          <div class="release form-group">
            {% if releases.count > 1 %}
              <label class="control-label">
                Release
                <span class="glyphicon glyphicon-question-sign get-help" title="The version of the build system you want to use for this project"></span>
              </label>
              <select name="projectversion" id="projectversion" class="form-control">
                {% for release in releases %}
                    <option value="{{release.id}}"
                        {%if defaultbranch == release.name %}
                            selected
                        {%endif%}
                     >{{release.description}}</option>
                {% endfor %}
              </select>
              <div class="row">
                <div class="col-md-4">
                {% for release in releases %}
                  <div class="helptext" id="description-{{release.id}}" style="display: none">
                    <span class="help-block">{{release.helptext|safe}}</span>
                  </div>
                {% endfor %}
                </div>
              </div>
            {% else %}
              <input type="hidden" name="projectversion" value="{{releases.0.id}}"/>
            {% endif %}

              <input type="checkbox" class="checkbox-mergeattr" name="mergeattr" value="mergeattr">  Merged Toaster settings (Command line user compatibility)
              <span class="glyphicon glyphicon-question-sign get-help" title="Place the Toaster settings into the standard 'local.conf' and 'bblayers.conf' instead of 'toaster_bblayers.conf' and 'toaster.conf'"></span>

           </div>
        {% endif %}

            <div class="build-import form-group" id="import-project">
              <label class="control-label">Import existing project directory
                <span class="glyphicon glyphicon-question-sign get-help" title="Enter a path to an existing build directory, import the existing settings, and create a Toaster Project for it."></span>
              </label>
              <input style="width: 33%;"type="text" class="form-control" required id="import-project-dir" name="importdir">
            </div>

            <div class="top-air">
              <input type="submit" id="create-project-button" class="btn btn-primary btn-lg" value="Create project"/>
              <span class="help-inline" style="vertical-align:middle;">To create a project, you need to enter a project name</span>
            </div>

        </form>
      </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            // hide the new project button
            $("#new-project-button").hide();
            $('.btn-primary').attr('disabled', 'disabled');
            $('#type-new').attr('checked', 'checked');

            // enable submit button when all required fields are populated
            $("input#new-project-name").on('input', function() {
                if ($("input#new-project-name").val().length > 0 ){
                    $('.btn-primary').removeAttr('disabled');
                    $(".help-inline").css('visibility','hidden');
                }
                else {
                    $('.btn-primary').attr('disabled', 'disabled');
                    $(".help-inline").css('visibility','visible');
                }
            });

            // show relevant help text for the selected release
            var selected_release = $('select').val();
            $("#description-" + selected_release).show();


            $('select').change(function(){
                var new_release = $('select').val();
                $(".helptext").hide();
                $('#description-' + new_release).fadeIn();
            });

            libtoaster.makeProjectNameValidation($("#new-project-name"),
                 $("#hint-error-project-name"), $("#validate-project-name"),
                 $(".btn-primary"));


			// Hide the project release when you select an analysis project
			function projectType() {
				if ($("input[type='radio']:checked").val() == 'new') {
					$('.build-import').fadeOut();
					$('.release').fadeIn();
                    $('#import-project-dir').removeAttr('required');
				}
				else {
					$('.release').fadeOut();
					$('.build-import').fadeIn();
                    $('#import-project-dir').attr('required', 'required');
				}
			}
			projectType();

			$('input:radio').change(function(){
				projectType();
			});
        });
    </script>

{% endblock %}
