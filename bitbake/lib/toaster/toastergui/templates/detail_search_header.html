{% comment %}
  Show a detail table Search field and Rows per page.
  Input:
    objects, our boilerplated paginated with search fields set.
    object_count, count of full, unfiltered, objects list
    search_what, fills in "Search ___"
  Only show the search form if we have more than 10 results,
  or if return from a previous search.
{% endcomment %}


<script>
$(document).ready(function() {
  /* Clear the current search selection and reload the results */
  $(".search-clear").click(function(){
    $("#search").val("");
    $(this).parents("form").submit();
  });
});
</script>

{% if objects.paginator.count > 10 or request.GET.search %}
  {% if objects.paginator.count == 0 %}
  <div class="alert alert-warning">
    <h4>No {{search_what}} found</h4>
    <form id="searchform" class="form-inline">
      {% else %}
      <form id="searchform" class="navbar-form navbar-left detail-page-controls">
        {% endif %}

        <div class="form-group">
          <div class="btn-group">
            <input id="search" class="form-control" type="text" placeholder="Search {{search_what}}" name="search" value="{% if request.GET.search %}{{request.GET.search}}{% endif %}">
            <input type="hidden" value="name:+" name="orderby">
            <input type="hidden" value="l" name="page">
            {% if request.GET.search %}
            <span class="remove-search-btn-detail-search search-clear glyphicon glyphicon-remove-circle"></span>
            {% endif %}
          </div>
        </div>
        <button type="submit" class="btn btn-default">Search</button>
        {% if objects.paginator.count == 0 %}
        <button type="submit" class="btn btn-link search-clear">
          Show all {{search_what}}
        </button>
        {% endif %}
      </form>
      {% endif %}

      {% if objects.paginator.count == 0 %}
  </div> {# end alert #}
  {% else %}
  {% if object_count > 10 %}
  <form class="navbar-form navbar-right">
    <div class="form-group">
      <label>Show rows:</label>
      <select class="pagesize form-control">
        {% with "10 25 50 100 150" as list%}
        {% for i in list.split %}
        {% if request.session.limit == i %}
        <option value="{{i}}" selected>{{i}}</option>
        {% else %}
        <option value="{{i}}">{{i}}</option>
        {% endif %}
        {% endfor %}
        {% endwith %}
      </select>
    </div>
  </form>
  {% endif %}
{% endif %}

