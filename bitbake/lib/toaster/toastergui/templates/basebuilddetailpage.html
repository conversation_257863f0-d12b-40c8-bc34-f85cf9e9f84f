{% extends "base.html" %}
{% load project_url_tag %}
{% load humanize %}
{% block pagecontent %}

<div class="row">
  <!-- Breadcrumbs -->
	<div class="col-md-12">
		<ul class="breadcrumb" id="breadcrumb">
			<li><a href="{% project_url build.project %}">{{build.project.name}}</a></li>
			{% if not build.project.is_default %}
			<li><a href="{% url 'projectbuilds' build.project.id %}">Builds</a></li>
			{% endif %}
			<li><a href="{%url 'builddashboard' build.pk%}">{{build.get_sorted_target_list.0.target}} {%if build.target_set.all.count > 1%}(+{{build.target_set.all.count|add:"-1"}}){%endif%} {{build.machine}} ({{build.completed_on|date:"d/m/y H:i"}})</a></li>
			{% block localbreadcrumb %}{% endblock %}
		</ul>
		<script>
$( function () {
	$('#breadcrumb > li').append('<span class="divider">&rarr;</span>');
	$('#breadcrumb > li:last').addClass("active");
	$('#breadcrumb > li:last > span').remove();
	});
		</script>
	</div>
</div>

<!-- Begin container -->
{% block pagedetailinfomain %}{% endblock %}
<!-- End container -->

{% endblock %}
