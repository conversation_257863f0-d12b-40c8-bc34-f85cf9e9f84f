{% extends "base.html" %}
{% load projecttags %}
{% load humanize %}
{% load static %}
{% block pagecontent %}

<link rel="stylesheet" href="{% static 'css/qunit-1.18.0.css' %}" />

<script src="{% static 'js/qunit-1.18.0.js' %}"></script>

<script src="{% static 'js/layerDepsModal.js' %}"></script>
<script src="{% static 'js/projectpage.js' %}"></script>

<script src="{% static 'js/bootstrap.min.js' %}"></script>
<script src="{% static 'js/filtersnippet.js' %}"></script>
<script src="{% static 'js/importlayer.js' %}"></script>
<script src="{% static 'js/highlight.pack.js' %}"></script>
<script src="{% static 'js/layerBtn.js' %}"></script>
<script src="{% static 'js/layerDepsModal.js' %}"></script>
<script src="{% static 'js/projectpage.js' %}"></script>
<script src="{% static 'js/layerdetails.js' %}"></script>
<script src="{% static 'js/table.js' %}"></script>

<script>
  var ctx = {
    tableUrl : '{% url 'projectlayers' project.pk %}',
    projectId : {{project.pk}},
  }
</script>
<script src="{% static 'js/tests/test.js' %}"></script>

<div id="qunit"></div>

<input type="text" id="layers" placeholder="layers" ></input>
<input type="text" id="recipes" placeholder="recipes"></input>
<input type="text" id="projects" placeholder="projects"></input>
<input type="text" id="machines" placeholder="machines"></input>

<!-- import layer dependency input typeahead -->
<input type="text" id="layer-dependency" style="display:none"></input>
<!-- project page input typeaheads -->
<input type="text" id="layer-add-input" style="display:none"></input>
<input type="text" id="machine-change-input" style="display:none"></input>
<!-- import layer dependency input typeahead on layer details edit layer -->
<input type="text" id="layer-dep-input" style="display:none"></input>

{% endblock %}
