{% load static %}
{% load humanize %}
{% load project_url_tag %}
<script src="{% static 'js/mrbsection.js' %}"></script>

{% if mru %}
  {% if mrb_type == 'project' %}
    <h2>
      Latest project builds

      {% if project.is_default %}
        <span class="glyphicon glyphicon-question-sign get-help heading-help" data-original-title="Builds in this project cannot be started from Toaster: they are started from the command line"></span>
        {% endif %}
    </h2>
  {% else %}
    <div class="page-header">
      <h1>Latest builds</h1>
    </div>
  {% endif %}

  <div id="latest-builds">
    {% for build in mru %}
      <div data-latest-build-result="{{build.id}}" class="alert build-result {% if build.outcome == build.SUCCEEDED %}alert-success{% elif build.outcome == build.FAILED %}alert-danger{% else %}alert-info{% endif %}">
        <!-- project title -->
        {% if mrb_type != 'project' %}
          <div class="row project-name">
            <div class="col-md-12">
              <small>
                <a class="alert-link text-uppercase" href="{% project_url build.project %}">
                  {{build.project.name}}
                </a>
              </small>
            </div>
          </div>
        {% endif %}

        <div class="row" data-role="build-status-container">
          <div class="col-md-12">
            Loading...
          </div>
        </div>
      </div>
    {% endfor %}
  </div>
{% endif %}

<!-- build main template -->
<script id="build-template" type="text/x-jsrender">
  <div class="col-md-3">
    <!-- only show link for completed builds -->
    <%if state == 'Succeeded' || state == 'Failed'%>
      <a class="alert-link" href="<%:dashboard_url%>">
        <span data-toggle="tooltip" data-role="targets-text" title="Recipes: <%:targets%>">
          <%:targets_abbreviated%>
        </span>
      </a>
    <%else targets_abbreviated !== ''%>
      <span data-toggle="tooltip" data-role="targets-text" title="Recipes: <%:targets%>">
        <%:targets_abbreviated%>
      </span>
    <%else%>
      Fetching recipe names...
    <%/if%>
  </div>

  <div data-build-state="<%:state%>">
    <%if state == 'Cloning'%>
      <%include tmpl='#cloning-repos-build-template'/%>
    <%else state == 'Parsing'%>
      <%include tmpl='#parsing-recipes-build-template'/%>
    <%else state == 'Queued'%>
      <%include tmpl='#queued-build-template'/%>
    <%else state == 'Succeeded' || state == 'Failed'%>
      <%include tmpl='#succeeded-or-failed-build-template'/%>
    <%else state == 'Cancelling'%>
      <%include tmpl='#cancelling-build-template'/%>
    <%else state == 'Starting'%>
      <%include tmpl='#starting-template'/%>
    <%else state == 'In Progress'%>
      <%include tmpl='#in-progress-build-template'/%>
    <%else state == 'Cancelled'%>
      <%include tmpl='#cancelled-build-template'/%>
    <%/if%>
  </div>
</script>

<!-- queued build -->
<script id="queued-build-template" type="text/x-jsrender">
  <div class="col-md-5">
    <span class="glyphicon glyphicon-question-sign get-help get-help-blue"
          title="This build is waiting for the build directory to become available">
    </span>

    Build queued
  </div>

  <div class="col-md-4">
    <!-- cancel button -->
    <%include tmpl='#cancel-template'/%>
  </div>
</script>

<!-- cloning repos build -->
<script id="cloning-repos-build-template" type="text/x-jsrender">
  <!-- progress bar and parse completion percentage -->
  <div data-role="build-status" class="col-md-4 col-md-offset-1 progress-info">
    <!-- progress bar -->
    <div class="progress">
      <div id="repos-cloned-percentage-bar-<%:id%>"
           style="width: <%:repos_cloned_percentage%>%;"
           class="progress-bar">
      </div>
    </div>
  </div>

  <div class="col-md-4 progress-info">
    <!-- parse completion percentage -->
    <span class="glyphicon glyphicon-question-sign get-help get-help-blue"
          title="Toaster is cloning the repos required for your build">
    </span>

    Cloning <span id="repos-cloned-percentage-<%:id%>"><%:repos_cloned_percentage%></span>% complete <span id="repos-cloned-progressitem-<%:id%>">(<%:progress_item%>)</span>

    <%include tmpl='#cancel-template'/%>
  </div>
</script>

<!-- parsing recipes build -->
<script id="parsing-recipes-build-template" type="text/x-jsrender">
  <!-- progress bar and parse completion percentage -->
  <div data-role="build-status" class="col-md-4 col-md-offset-1 progress-info">
    <!-- progress bar -->
    <div class="progress">
      <div id="recipes-parsed-percentage-bar-<%:id%>"
           style="width: <%:recipes_parsed_percentage%>%;"
           class="progress-bar">
      </div>
    </div>
  </div>

  <div class="col-md-4 progress-info">
    <!-- parse completion percentage -->
    <span class="glyphicon glyphicon-question-sign get-help get-help-blue"
          title="BitBake is parsing the layers required for your build">
    </span>

    Parsing <span id="recipes-parsed-percentage-<%:id%>"><%:recipes_parsed_percentage%></span>% complete

    <%include tmpl='#cancel-template'/%>
  </div>
</script>

<!-- in progress build; tasks still starting -->
<script id="starting-template" type="text/x-jsrender">
  <div class="col-md-5">
    <span class="glyphicon glyphicon-question-sign get-help get-help-blue"
          title="This build is waiting for tasks to start">
    </span>

    Tasks starting...
  </div>

  <div class="col-md-4">
    <!-- cancel button -->
    <%include tmpl='#cancel-template'/%>
  </div>
</script>

<!-- in progress build; at least one task finished -->
<script id="in-progress-build-template" type="text/x-jsrender">
  <!-- progress bar and task completion percentage -->
  <div data-role="build-status" class="col-md-4 col-md-offset-1 progress-info">
    <!-- progress bar -->
    <div class="progress" id="build-pc-done-title-<%:id%>">
      <div id="build-pc-done-bar-<%:id%>"
           style="width: <%:tasks_complete_percentage%>%;"
           class="progress-bar">
      </div>
    </div>
  </div>

  <div class="col-md-4 progress-info">
    <!-- task completion percentage -->
    <span id="build-pc-done-<%:id%>"><%:tasks_complete_percentage%></span>% of
    tasks complete

    <!-- cancel button -->
    <%include tmpl='#cancel-template'/%>
  </div>
</script>

<!-- cancelling build -->
<script id="cancelling-build-template" type="text/x-jsrender">
  <div class="col-md-9">
    Cancelling the build ...
  </div>
</script>

<!-- succeeded or failed build -->
<script id="succeeded-or-failed-build-template" type="text/x-jsrender">
  <!-- completed_on -->
  <div class="col-md-2">
    <%:completed_on%>
  </div>

  <!-- errors -->
  <div class="col-md-2">
    <%if errors%>
      <span class="glyphicon glyphicon-minus-sign"></span>
      <a href="<%:dashboard_errors_url%>" class="alert-link">
        <%:errors%> error<%:errors_pluralise%>
      </a>
    <%/if%>
  </div>

  <!-- warnings -->
  <div class="col-md-2">
    <%if warnings%>
      <span class="glyphicon glyphicon-warning-sign build-warnings"></span>
      <a href="<%:dashboard_warnings_url%>" class="alert-link build-warnings">
        <%:warnings%> warning<%:warnings_pluralise%>
      </a>
    <%/if%>
  </div>

  <!-- build time -->
  <div class="col-md-3">
    Build time:

    <span data-role="data-recent-build-buildtime-field">
      <%if state == 'Succeeded'%>
        <a class="alert-link" href="<%:buildtime_url%>"><%:buildtime%></a>
      <%else%>
        <%:buildtime%>
      <%/if%>
    </span>

    <!-- rebuild button -->
    <%include tmpl='#rebuild-template'/%>
  </div>
</script>

<!-- cancelled build -->
<script id="cancelled-build-template" type="text/x-jsrender">
  <!-- build cancelled message -->
  <div class="col-md-6">
    Build cancelled
  </div>

  <!-- rebuild button -->
  <div class="col-md-3">
    <%include tmpl='#rebuild-template'/%>
  </div>
</script>

<!-- rebuild button or no rebuild icon -->
<script id="rebuild-template" type="text/x-jsrender">
  <%if is_default_project_build%>
    <!-- no rebuild info icon -->
    <span class="pull-right glyphicon glyphicon-question-sign get-help <%if state == 'Succeeded'%>get-help-green<%else state == 'Failed'%>get-help-red<%else%>get-help-blue<%/if%>"
    title="Builds in this project cannot be started from Toaster: they are started from the command line">
    </span>
  <%else%>
    <!-- rebuild button -->
    <span class="rebuild-btn alert-link <%if state == 'Success'%>success<%else state == 'Failed'%>danger<%else%>info<%/if%> pull-right"
    data-request-url="<%:rebuild_url%>" data-target='<%:build_targets_json%>'>
      <span class="glyphicon glyphicon-repeat"></span>
      Rebuild
    </span>
  <%/if%>
</script>

<!-- cancel button or no cancel icon -->
<script id="cancel-template" type="text/x-jsrender">
  <%if is_default_project_build%>
    <!-- no cancel icon -->
    <span class="glyphicon glyphicon-question-sign get-help get-help-blue pull-right" title="Builds in this project cannot be cancelled from Toaster: they can only be cancelled from the command line"></span>
  <%else%>
    <!-- cancel button -->
    <span class="cancel-build-btn pull-right alert-link"
    data-buildrequest-id="<%:buildrequest_id%>" data-request-url="<%:cancel_url%>">
      <span class="glyphicon glyphicon-remove-circle"></span>
      Cancel
    </span>
  <%/if%>
</script>

<script>
  $(document).ready(function () {
    var ctx = {
      mrbType : "{{mrb_type}}",
    }

    try {
      mrbSectionInit(ctx);
    } catch (e) {
      document.write("Sorry, An error has occurred loading this page");
      console.warn(e);
    }
  });
</script>
