{% extends "baseprojectbuildspage.html" %}
{% load projecttags %}
{% load humanize %}

{% block title %} Builds - {{project.name}} - Toaster {% endblock %}
{% block extraheadcontent %}
<link rel="stylesheet" href="/static/css/jquery-ui.min.css" type='text/css'>
<link rel="stylesheet" href="/static/css/jquery-ui.structure.min.css" type='text/css'>
<link rel="stylesheet" href="/static/css/jquery-ui.theme.min.css" type='text/css'>
<script src="/static/js/jquery-ui.min.js"></script>
<script src="/static/js/filtersnippet.js"></script>
{% endblock %}

{% block projectinfomain %}

<script>
    // initialize the date range controls
    $(document).ready(function () {
        date_init('started_on','{{last_date_from}}','{{last_date_to}}','{{dateMin_started_on}}','{{dateMax_started_on}}','{{daterange_selected}}');
        date_init('completed_on','{{last_date_from}}','{{last_date_to}}','{{dateMin_completed_on}}','{{dateMax_completed_on}}','{{daterange_selected}}');
    });
</script>

 {% with mrb_type='project' %}
     {% include "mrb_section.html" %}
 {% endwith %}

      <h2>
        {% if request.GET.filter and objects.paginator.count > 0 or request.GET.search and objects.paginator.count > 0 %}
            {{objects.paginator.count}} project build{{objects.paginator.count|pluralize}} found
        {%elif request.GET.filter and objects.paginator.count == 0 or request.GET.search and objects.paginator.count == 0 %}
            No project builds found
        {%else%}
            All project builds
        {%endif%}
        <i class="icon-question-sign get-help heading-help" title="This page lists all the builds for the current project"></i>
      </h2>


 {% if objects.paginator.count == 0 %}
  {% if request.GET.filter or request.GET.search %}
    <div class="row">
      <div class="alert">
        <form class="no-results input-append" id="searchform">
            <input id="search" name="search" class="input-xxlarge" type="text" value="{{request.GET.search}}"/>{% if request.GET.search %}<a href="javascript:$('#search').val('');searchform.submit()" class="input-append-addon btn" tabindex="-1"><i class="glyphicon glyphicon-remove"></i></a>{% endif %}
            <button class="btn" type="submit" value="Search">Search</button>
            <button class="btn btn-link" onclick="javascript:$('#search').val('');searchform.submit()">Show all builds</button>
        </form>
      </div>
    </div>
  {% else %}
    <div class="alert alert-info">
      <p class="lead">
        This project has no builds.
      </p>
    </div>
  {% endif %}

 {% else %}

  {% include "basetable_top.html" %}
        <!-- Table data rows; the order needs to match the order of "tablecols" definitions; and the <td class value needs to match the tablecols clclass value for show/hide buttons to work -->
        {% for build in objects %} {# if we have a build, just display it #}
        <tr class="data">
            <td class="outcome"><a href="{% url "builddashboard" build.id %}">{%if build.outcome == build.SUCCEEDED%}<i class="icon-ok-sign success"></i>{%elif build.outcome == build.FAILED%}<i class="icon-minus-sign error"></i>{%else%}{%endif%}</a>
              {% if build.cooker_log_path %}
                &nbsp;
                <a href="{% url 'build_artifact' build.id "cookerlog" build.id %}">
                  <i class="icon-download-alt" title="Download build log"></i>
                </a>
              {% endif %}
            </td>

            <td class="target">
                {% for t in build.target_set.all %}
                    <a href="{% url "builddashboard" build.id %}">
                        {% if t.task %}
                            {{t.target}}:{{t.task}}
                        {% else %}
                            {{t.target}}
                        {% endif %}
                    </a> <br />
                {% endfor %}
            </td>
            <td class="machine"><a href="{% url "builddashboard" build.id %}">{{build.machine}}</a></td>
            <td class="started_on"><a href="{% url "builddashboard" build.id %}">{{build.started_on|date:"d/m/y H:i"}}</a></td>
            <td class="completed_on"><a href="{% url "builddashboard" build.id %}">{{build.completed_on|date:"d/m/y H:i"}}</a></td>
            <td class="failed_tasks error">
                {% query build.task_build outcome=4 order__gt=0 as exectask%}
                    {% if exectask.count == 1 %}
                        <a href="{% url "task" build.id exectask.0.id %}">{{exectask.0.recipe.name}}.{{exectask.0.task_name}}</a>
                        <a href="{% url 'build_artifact' build.id "tasklogfile" exectask.0.id %}">
                            <i class="icon-download-alt" title="" data-original-title="Download task log file"></i>
                        </a>
                    {% elif exectask.count > 1%}
                        <a href="{% url "tasks" build.id %}?filter=outcome%3A4">{{exectask.count}} task{{exectask.count|pluralize}}</a>
                    {%endif%}
            </td>
            <td class="errors.count">
                {% if  build.errors.count %}
                    <a class="errors.count error" href="{% url "builddashboard" build.id %}#errors">{{build.errors.count}} error{{build.errors.count|pluralize}}</a>
                {%endif%}
            </td>
            <td class="warnings.count">{% if  build.warnings.count %}<a class="warnings.count warning" href="{% url "builddashboard" build.id %}#warnings">{{build.warnings.count}} warning{{build.warnings.count|pluralize}}</a>{%endif%}</td>
            <td class="time"><a href="{% url "buildtime" build.id %}">{{build.timespent_seconds|sectohms}}</a></td>
            <td class="output">
              {% if build.outcome == build.SUCCEEDED %}
              <a href="{%url "builddashboard" build.id%}#images">{{fstypes|get_dict_value:build.id}}</a>
              {% endif %}
            </td>
        </tr>
        {% endfor %}


  {% include "basetable_bottom.html" %}
{% endif %}

{% endblock %}
