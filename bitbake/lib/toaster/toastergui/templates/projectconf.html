{% extends project_specific|yesno:"baseprojectspecificpage.html,baseprojectpage.html" %}
{% load projecttags %}
{% load humanize %}

{% block title %} BitBake variables - {{project.name}} - Toaster {% endblock %}
{% block projectinfomain %}

<h2>Bitbake variables</h2>
<div>
  <dl>
    {% if distro_defined %}
    <dt>
    <span class="js-config-var-name js-config-var-managed-name">DISTRO</span>
    <span class="glyphicon glyphicon-question-sign get-help" title="The short name of the distribution. If the variable is blank, meta/conf/distro/defaultsetup.conf will be used"></span>
    </dt>
    <dd class="variable-list">
    <span class="lead" id="distro">{{distro}}</span>
    <span class="glyphicon glyphicon-edit" id="change-distro-icon"></span>
    <form id="change-distro-form" class="form-inline" style="display:none;">
      <div id="edit-distro-name-div" class="form-group">
        <input type="text" class="form-control" id="new-distro" value="{{distro}}">
      </div>
      <button id="apply-change-distro" class="btn btn-default" type="button">Save</button>
      <button id="cancel-change-distro" type="button" class="btn btn-link">Cancel</button>
      <span class="help-block" id="distro-error-message"></span>
    </form>
    </dd>
    {% endif %}

    {% if dl_dir_defined %}
    <dt>
    <span class="js-config-var-name js-config-var-managed-name">DL_DIR</span>
    <span class="glyphicon glyphicon-question-sign get-help" title="Absolute path to the directory used to store downloads required for your builds. By default, Toaster projects share the same downloads directory"></span>
    </dt>
    <dd class="variable-list">
    <span id="dl_dir" class="lead {% if not dl_dir %} text-muted {% endif %}">{% if dl_dir %}{{dl_dir}}{%else%}Not set{%endif%}</span>
    <span class="glyphicon glyphicon-edit" id="change-dl_dir-icon"></span>
    <form id="change-dl_dir-form" class="form-inline" style="display:none;">
      <div class="form-group" id="validate-dl_dir">
        <input type="text" class="form-control" id="new-dl_dir" placeholder="Type an absolute path">
      </div>
      <button id="apply-change-dl_dir" class="btn btn-default" type="button">Save</button>
      <button id="cancel-change-dl_dir" type="button" class="btn btn-link">Cancel</button>
      <p class="help-block" id="hintError-dl_dir" style="display:none;">The directory path cannot include spaces or any of these characters: \ ? % * : | " " &lt; &gt;</p>
      <p class="help-block" id="hintError-initialChar-dl_dir" style="display:none;">The directory path should either start with a /, e.g. /home/<USER>/downloads; or with a variable, e.g. ${TOPDIR}/downloads.</p>
    </form>
    </dd>
    {% endif %}

    {% if fstypes_defined %}
    <dt>
    <span class="js-config-var-name js-config-var-managed-name">IMAGE_FSTYPES</span>
    <span class="glyphicon glyphicon-question-sign get-help" title="Formats of root file system images that you want to create"></span>
    </dt>
    <dd class="variable-list">
    <span class="lead" id="image_fstypes">{{fstypes}}</span>
    <span class="glyphicon glyphicon-edit" id="change-image_fstypes-icon"></span>
    <form id="change-image_fstypes-form" style="display:none;">
      <label>Type the image types you want to build:</label>
      <div class="form-group form-inline" id="validate-image_fstypes">
        <input type="text" class="form-control "id="new-imagefs_types">
        <button id="apply-change-image_fstypes" type="button" class="btn btn-default">Save</button>
        <button id="cancel-change-image_fstypes" type="button" class="btn btn-link">Cancel</button>
      </div>
      <p class="help-block text-danger" style="display:none;" id="hintError-image-fs_type">A valid image type cannot include underscores</p>
      <p class="help-block text-danger" style="display:none;" id="fstypes-error-message">You must select at least one image type</p>
      <label>Or choose from known image types:</label>
      <input id="filter-image_fstypes" type="text" placeholder="Search image types" class="form-control">
      <div id="all-image_fstypes" class="scrolling"></div>
    </form>
    </dd>
    {% endif %}

    {% if image_install_append_defined %}
    <dt>
    <span class="js-config-var-name js-config-var-managed-name">IMAGE_INSTALL:append</span>
    <span class="glyphicon glyphicon-question-sign get-help" title="Specifies additional packages to install into an image. If your build creates more than one image, the packages will be installed in all of them"></span>
    </dt>
    <dd class="variable-list">
    <span id="image_install" class="lead {% if not image_install_append %} text-muted {%endif%}">{% if image_install_append %}{{image_install_append}}{%else%}Not set{%endif%}</span>
    <span class="glyphicon glyphicon-edit" id="change-image_install-icon"></span>
    <span class="glyphicon glyphicon-trash" id="delete-image_install-icon" {% if image_install_append %}{%else%}style="display:none;"{%endif%}></span>
    <form id="change-image_install-form" class="form-inline" style="display:none;">
      <div class="row">
        <div class="col-md-4">
          <span class="help-block">To set IMAGE_INSTALL:append to more than one package, type the package names separated by a space.</span>
        </div>
      </div>
      <div class="form-group">
        <input type="text" class="form-control" id="new-image_install" placeholder="Type one or more package names">
      </div>
      <button id="apply-change-image_install" class="btn btn-default" type="button">Save</button>
      <button id="cancel-change-image_install" type="button" class="btn btn-link">Cancel</button>
    </form>
    </dd>
    {% endif %}

    {% if package_classes_defined %}
    <dt>
    <span class="js-config-var-name js-config-var-managed-name">PACKAGE_CLASSES</span>
    <span class="glyphicon glyphicon-question-sign get-help" title="Specifies the package manager to use when packaging data"></span>
    </dt>
    <dd class="variable-list">
    <span class="lead" id="package_classes">{{package_classes}}</span>
    <span id="change-package_classes-icon" class="glyphicon glyphicon-edit"></span>
    <form id="change-package_classes-form" style="display:none;">
      <div class="form-group">
        <label class="control-label">
          Root file system package format
          <span class="glyphicon glyphicon-question-sign get-help" title="The package format used to generate the root file system. Options are <code>deb</code>, <code>ipk</code> and <code>rpm</code>"></i>
        </label>
        <select id="package_classes-select" class="form-control">
          <option>package_deb</option>
          <option>package_ipk</option>
          <option>package_rpm</option>
        </select>
      </div>
      <div class="form-group">
        <label class="control-label">
          Additional package formats
          <span class="glyphicon glyphicon-question-sign get-help" title="Extra package formats to build"></span>
        </label>
        <div class="checkbox">
          <label id="package_class_1">
            <input type="checkbox" id="package_class_1_input"> package_deb
          </label>
        </div>
        <div class="checkbox">
          <label id="package_class_2">
            <input type="checkbox" id="package_class_2_input"> package_ipk
          </label>
        </div>
      </div>
      <button id="apply-change-package_classes" type="button" class="btn btn-default">Save</button>
      <button id="cancel-change-package_classes" type="button" class="btn btn-link">Cancel</button>
    </form>
    </dd>
    {% endif %}

    {% if sstate_dir_defined %}
    <dt>
    <span class="js-config-var-name js-config-var-managed-name">SSTATE_DIR</span>
    <span class="glyphicon glyphicon-question-sign get-help" title="Absolute path to the directory used to store shared state cache files. These files are reused across the builds, which makes the builds faster. By default, Toaster projects share the same cache directory"></span>
    </dt>
    <dd class="variable-list">
    <span id="sstate_dir" class="lead {% if not sstate_dir %} text-muted {% endif %}">{% if sstate_dir %}{{sstate_dir}}{%else%}Not set{%endif%}</span>
    <span class="glyphicon glyphicon-edit" id="change-sstate_dir-icon"></span>
    <form class="form-inline" id="change-sstate_dir-form" style="display:none;">
      <div class="form-group" id="validate-sstate_dir">
        <input type="text" class="form-control" id="new-sstate_dir" placeholder="Type an absolute path">
      </div>
      <button id="apply-change-sstate_dir" class="btn btn-default" type="button">Save</button>
      <button id="cancel-change-sstate_dir" type="button" class="btn btn-link">Cancel</button>
      <p class="help-block" id="hintError-sstate_dir" style="display:none;">The directory path cannot include spaces or any of these characters: \ ? % * : | " " &lt; &gt;</p>
      <p class="help-block" id="hintError-initialChar-sstate_dir" style="display:none;">The directory path should either start with a /, e.g. /home/<USER>/sstate-cache; or with a variable, e.g. ${TOPDIR}/sstate-cache.</p>
    </form>
    </dd>
    {% endif %}
  </dl>

  <!-- <ul class="list-unstyled configuration-list" id="configvar-list"> -->
  <dl id="configvar-list">
    <!-- the added configuration variables are inserted here -->
  </dl>

  <!-- pass the fstypes list, black list, and externally managed variables here -->
  {% for fstype in vars_fstypes %}
  <input type="hidden" class="js-checkbox-fstypes-list" value="{{fstype}}">
  {% endfor %}
  {% for b in vars_disallowed %}
  <input type="hidden" class="js-config-disallowed-name" value="{{b}}">
  {% endfor %}
  {% for b in vars_managed %}
  <input type="hidden" class="js-config-managed-name" value="{{b}}">
  {% endfor %}

  <form id="variable-form">
    <fieldset>
      <legend>Add variable</legend>
      <div class="row">
        <div class="col-md-3">
          <div id="add-configvar-name-div" class="form-group">
            <label class="control-label">
              Variable
              <span class="glyphicon glyphicon-question-sign get-help"
                    title="Variable names are case sensitive,
                    cannot have spaces, and can only include letters, numbers, underscores
                    and dashes"></span>
            </label>
            <input type="text" class="form-control" placeholder="Type the variable name" id="variable">
          </div>
          <p class="help-block" id="new-variable-error-message"></p>
          <div class="form-group">
            <label clas="control-label">Value</label>
            <input id="value" type="text" class="form-control" placeholder="Type the variable value">
          </div>
          <button id="add-configvar-button" class="btn btn-default save" type="button" disabled>Add variable</button>
        </div>
        <div class="col-md-5 help-block">
          <h5>Some variables cannot be set from Toaster</h5>
          <p>Toaster cannot set any variables that impact 1) the configuration of the build servers,
          or 2) where artifacts produced by the build are stored. Such variables include: </p>
          <p>
          <code><a href="http://docs.yoctoproject.org/ref-manual/variables.html#term-BB_DISKMON_DIRS" target="_blank">BB_DISKMON_DIRS</a></code>
          <code><a href="http://docs.yoctoproject.org/ref-manual/variables.html#term-BB_NUMBER_THREADS" target="_blank">BB_NUMBER_THREADS</a></code>
          <code>CVS_PROXY_HOST</code>
          <code>CVS_PROXY_PORT</code>
          <code><a href="http://docs.yoctoproject.org/ref-manual/variables.html#term-PARALLEL_MAKE" target="_blank">PARALLEL_MAKE</a></code>
          <code><a href="http://docs.yoctoproject.org/ref-manual/variables.html#term-TMPDIR" target="_blank">TMPDIR</a></code></p>
          <p>Plus the following standard shell environment variables:</p>
          <p><code>http_proxy</code> <code>ftp_proxy</code> <code>https_proxy</code> <code>all_proxy</code></p>
        </div>
      </div>
    </fieldset>
  </form>
</div>

</div>

<script>

// global variables
var do_reload=false;

// validate new variable name
function validate_new_variable() {
  var variable = $("input#variable").val();
  var value    = $("input#value").val();

  // presumed innocence
  $('#new-variable-error-message').text("");
  var error_msg = "";

  var existing_configvars = document.getElementsByClassName('js-config-var-name');
  for (var i = 0, length = existing_configvars.length; i < length; i++) {
    if (existing_configvars[i].innerHTML.toUpperCase() == variable.toUpperCase()) {
      error_msg = "This variable is already set in this page. Edit its value instead";
    }
  }

  var disallowed_configvars = document.getElementsByClassName('js-config-disallowed-name');
  for (var i = 0, length = disallowed_configvars.length; i < length; i++) {
    if (disallowed_configvars[i].value.toUpperCase() == variable.toUpperCase()) {
      error_msg = "You cannot edit this variable in Toaster because it is set by the build servers";
    }
  }

  var managed_configvars = document.getElementsByClassName('js-config-managed-name');
  for (var i = 0, length = managed_configvars.length; i < length; i++) {
    if (managed_configvars[i].value.toUpperCase() == variable.toUpperCase()) {
      error_msg = "You cannot set this variable here. Please set it in the <a href=\"{% url 'project' project.id %}\">project main page</a>";
    }
  }

  var bad_chars  = /[^a-zA-Z0-9\-_/]/.test(variable);
  var has_spaces = (0 <= variable.indexOf(" "));
  var only_spaces = (0 < variable.length) && (0 == variable.trim().length);

  if (only_spaces) {
    error_msg = "A valid variable name cannot include spaces";
  } else if (bad_chars && has_spaces) {
    error_msg = "A valid variable name can only include letters, numbers and the special characters <code> _ - /</code>. Variable names cannot include spaces";
  } else if (bad_chars) {
    error_msg = "A valid variable name can only include letters, numbers and the special characters <code>_ - /</code>";
  }

  if ("" != error_msg) {
    $('#new-variable-error-message').html(error_msg);
    $(".save").attr("disabled","disabled");

    // add one (and only one) error class append
    $("#add-configvar-name-div").addClass("has-error");
    $("#new-variable-error-message").addClass("text-danger");

    return false;
  } else if (0 == variable.length) {
    $(".save").attr("disabled","disabled");
    return false;
  }

  $("#add-configvar-name-div").removeClass("has-error");

  // now set the "Save" enablement if 'value' also passes
  if (value.trim().length > 0) {
    $(".save").removeAttr("disabled");
  } else {
    $(".save").attr("disabled","disabled");
  }

  return true;
}

// validate distro name
function validate_distro_name() {
  var value    = $("input#new-distro").val();

  // presumed innocence
  $('#distro-error-message').text("");
  var error_msg = "";

  var has_spaces = (0 <= value.indexOf(" "));

  if (has_spaces) {
    error_msg = "A valid distro name cannot include spaces";
  } else if (0 == value.length) {
    error_msg = " ";
  }

  if ("" != error_msg) {
    $('#distro-error-message').text(error_msg);
    $("#apply-change-distro").attr("disabled","disabled");

    // add one (and only one) error class append
    $("#change-distro-form").addClass("has-error");

    return false;
  }

  $("#change-distro-form").removeClass("has-error");
  $("#apply-change-distro").removeAttr("disabled");
  return true;
}

// Test to insure at least one FS Type is checked
function enableFsTypesSave() {
  var any_checked = 0;
  $(".fs-checkbox-fstypes:checked").each(function(){
    any_checked = 1;
  });
  if ( 0 == any_checked ) {
    $("#apply-change-image_fstypes").attr("disabled","disabled");
    $('.scrolling').addClass('has-error');
    $('#fstypes-error-message').show();
  }
  else {
    $("#apply-change-image_fstypes").removeAttr("disabled");
    $('.scrolling').removeClass('has-error');
    $('#fstypes-error-message').hide();
  }
}

// Preset or reset the Package Class checkbox labels
function updatePackageClassCheckboxes() {
  $('#package_class_1, #package_class_2').hide();
  if ($('select').val() == 'package_deb') {
    $('#package_class_1').html('<input type="checkbox" id="package_class_1_input"> package_ipk');
    $('#package_class_2').html('<input type="checkbox" id="package_class_2_input"> package_rpm');
  }
  if ($('select').val() == 'package_ipk') {
    $('#package_class_1').html('<input type="checkbox" id="package_class_1_input"> package_deb');
    $('#package_class_2').html('<input type="checkbox" id="package_class_2_input"> package_rpm');
  }
  if ($('select').val() == 'package_rpm') {
    $('#package_class_1').html('<input type="checkbox" id="package_class_1_input"> package_deb');
    $('#package_class_2').html('<input type="checkbox" id="package_class_2_input"> package_ipk');
  }
  $('#package_class_1, #package_class_2').fadeIn(1500);
}

// Re-assert handlers when the page is served and/or refreshed via Ajax
function setEventHandlersForDynamicElements() {

  // change variable value
  $('.js-icon-pencil-config_var').click(function (evt) {
    var pk = $(this).attr("x-data");
    var current_val = $("#config_var_value_"+pk).text();
    $("#config_var_value_"+pk).hide();
    $("#config_var_trash_"+pk).hide();
    $(".js-icon-pencil-config_var[x-data="+pk+"]").hide();
    $("#change-config_var-form_"+pk).slideDown();
    $("#new-config_var_"+pk).val(current_val);
    if ( $("#new-config_var_"+pk).val().length ) {
      $("#apply-change-config_var_"+pk).removeAttr("disabled");
    }
    else {
      $("#apply-change-config_var_"+pk).attr("disabled");
    }
  });

  $('.js-cancel-change-config_var').click(function (evt) {
    var pk = evt.target.attributes["x-data"].value;
    $("#change-config_var-form_"+pk).slideUp(function() {
      $("#config_var_trash_"+pk).show();
      $('#config_var_value_'+pk).show();
      $(".js-icon-pencil-config_var[x-data="+pk+"]").show();
    });
  });

  $(".js-new-config_var").on('input', function(){
    if ($(this).val().length == 0) {
      $(this).parent("div").next(".btn-default").attr("disabled","disabled");
    }
    else {
      $(this).parent("div").next(".btn-default").removeAttr("disabled");
    }
  });

  $('.js-apply-change-config_var').click(function (evt) {
    var xdata    = evt.target.attributes["x-data"].value.split(":");
    var pk       = xdata[0];
    var variable = xdata[1];
    var val      = $('#new-config_var_'+pk).val();
    postEditAjaxRequest({"configvarChange" : variable+':'+val});
    $("#change-config_var-form_"+pk).slideUp();
    $("#config_var_trash_"+pk).fadeIn();
    $('#config_var_value_'+pk).fadeIn();
    $(".js-icon-pencil-config_var[x-data="+pk+"]").fadeIn();
  });

  // delete variable
  $(".js-icon-trash-config_var").click(function (evt) {
    var pk = $(this).attr("x-data");

    // fade out the variable+value div, then refresh the variable list
    $(this).fadeOut();
    $(this).tooltip("hide");
    $("config_var_entry_"+pk).fadeOut();
    $('#config_var_value_'+pk).parent("dd").fadeOut();
    postEditAjaxRequest({"configvarDel": evt.target.attributes["x-data"].value});
  });

}

function onEditPageUpdate(data) {
  // update targets
  var i; var orightml = "";

  var configvars_sorted = data.configvars.sort(function(a, b){return a[0] > b[0]});

  var managed_configvars = document.getElementsByClassName('js-config-var-managed-name');

  for (i = 0; i < configvars_sorted.length; i++) {
    // skip if the variable name has a special context (not user defined)
    var var_context=undefined;
    for (var j = 0, length = managed_configvars.length; j < length; j++) {
      if ((managed_configvars[j].innerHTML == configvars_sorted[i][0]) ||
          (managed_configvars[j].value     == configvars_sorted[i][0]) ) {
        var_context='m';
      }
    }
    if (configvars_sorted[i][0].startsWith("INTERNAL_")) {
        var_context='m';
    }
    if (var_context == undefined) {
        orightml += '<dt><span id="config_var_entry_'+configvars_sorted[i][2]+'" class="js-config-var-name"></span><span class="glyphicon glyphicon-trash js-icon-trash-config_var" id="config_var_trash_'+configvars_sorted[i][2]+'" x-data="'+configvars_sorted[i][2]+'"></span> </dt>'
        orightml += '<dd class="variable-list">'
        orightml += '    <span class="lead" id="config_var_value_'+configvars_sorted[i][2]+'"></span>'
        orightml += '    <span class="glyphicon glyphicon-edit js-icon-pencil-config_var" x-data="'+configvars_sorted[i][2]+'"></span>'
        orightml += '    <form class="form-inline" id="change-config_var-form_'+configvars_sorted[i][2]+'" style="display:none;">'
        orightml += '        <div class="form-group">'
        orightml += '            <input type="text" class="form-control js-new-config_var" id="new-config_var_'+configvars_sorted[i][2]+'" value=""></div>'
        orightml += '            <button id="apply-change-config_var_'+configvars_sorted[i][2]+'" class="btn btn-default js-apply-change-config_var" type="button" x-data="'+configvars_sorted[i][2]+':'+configvars_sorted[i][0]+'" disabled>Save</button>'
        orightml += '            <button type="button" class="btn btn-link js-cancel-change-config_var" x-data="'+configvars_sorted[i][2]+'">Cancel</button>'
        orightml += '    </form>'
        orightml += '</dd>'
    }
  }

  // update configvars list HTML framework
  $("dl#configvar-list").html(orightml);

  // insert the name/value pairs safely as non-HTML
  for (i = 0; i < configvars_sorted.length; i++) {
    $('#config_var_entry_'+configvars_sorted[i][2]).text(configvars_sorted[i][0]);
    $('#config_var_value_'+configvars_sorted[i][2]).text(configvars_sorted[i][1]);
  }

  // Add the tooltips
  $(".js-icon-trash-config_var").each( function(){ setDeleteTooltip($(this)); });
  $(".js-icon-pencil-config_var").each(function(){ setChangeTooltip($(this)); });

  // re-assert these event handlers
  setEventHandlersForDynamicElements();
}

function onEditAjaxSuccess(data, textstatus) {
  console.log("XHR returned:", data, "(" + textstatus + ")");
  if (data.error != "ok") {
    alert("error on request:\n" + data.error);
    return;
  }

  // delayed page reload?
  if (do_reload) {
    do_reload=false;
    location.reload(true);
  } else {
    onEditPageUpdate(data);
  }
}

function onEditAjaxError(jqXHR, textstatus, error) {
  alert("XHR errored:\n" + error + "\n(" + textstatus + ")");
  // re-assert the event handlers
}

/* ensure cookie exists {% csrf_token %} */
function postEditAjaxRequest(reqdata) {
  var ajax = $.ajax({
    type:"POST",
    data: $.param(reqdata),
    url:"{% url 'xhr_configvaredit' project.id%}",
    headers: { 'X-CSRFToken': $.cookie("csrftoken")},
    success: onEditAjaxSuccess,
    error: onEditAjaxError,
  })
}

function setDeleteTooltip(object) {
  object.tooltip({ container: 'body', html: true, delay: {show: 400}, title: "Delete" });
}
function setChangeTooltip(object) {
  object.tooltip({ container: 'body', html: true, delay: {show: 400}, title: "Change" });
}

$(document).ready(function() {

  //
  // Register handlers for static elements
  //

  {% if distro_defined %}
  // change distro variable
  $('#change-distro-icon').click(function() {
    $('#change-distro-icon, #distro').hide();
    $("#change-distro-form").slideDown();
    $("#new-distro").val( $('#distro').text() );
    $("#apply-change-distro").removeAttr("disabled");
  });

  $('#cancel-change-distro').click(function(){
    $("#change-distro-form").slideUp(function() {
      $('#distro, #change-distro-icon').show();

      // reset any dangling error state
      $('#distro-error-message').text("");
      $("#change-distro-form").removeClass("has-error");
    });
  });

  // validate new distro name
  $("input#new-distro").on('input', function (evt) {
    validate_distro_name();
  });

  $('#apply-change-distro').click(function(){
    //$('#repo').parent().removeClass('highlight-go');
    var name = $('#new-distro').val();
    postEditAjaxRequest({"configvarChange" : 'DISTRO:'+name});
    $('#distro').text(name);
    $("#change-distro-form").slideUp(function () {
      $('#distro, #change-distro-icon').show();
    });
  });
  {% endif %}

  {% if dl_dir_defined %}

  // change DL_DIR variable
  $('#change-dl_dir-icon').click(function() {
    $('#change-dl_dir-form').removeClass('has-error');
    // preset the edit value
    var current_val = $("#dl_dir").text().trim();
    if (current_val == "Not set") {
      current_val="";
      $("#apply-change-dl_dir").attr("disabled","disabled");
    }
    $("input#new-dl_dir").val(current_val);
    // enable / disable the save button based on the input value
    if ( current_val.length ) {
      $("#apply-change-dl_dir").removeAttr("disabled");
    }
    else {
      $("#apply-change-dl_dir").attr("disabled","disabled");
    }

    $('#change-dl_dir-icon, #dl_dir').hide();
    $("#change-dl_dir-form").slideDown();
  });

  $('#cancel-change-dl_dir').click(function(){
    $("#hintError-dl_dir").hide();
    $("#hintError-initialChar-dl_dir").hide();
    $("#change-dl_dir-form").slideUp(function() {
      $('#dl_dir, #change-dl_dir-icon').show();
    });
  });

  $("#new-dl_dir").on('input', function(){
    if ($(this).val().trim().length == 0) {
      $("#apply-change-dl_dir").attr("disabled","disabled");
      $('#change-dl_dir-form').addClass('has-error');
      $('#hintError-dl_dir').hide();
      $('#hintError-initialChar-dl_dir').hide();
    }
    else {
      var input = $(this);
      var reBeginWithSlash = /^\//;
      var reCheckVariable = /^\$/;
      var re = /([ <>\\|":%\?\*]+)/;
      var invalidDir = re.test(input.val());
      var invalidSlash = reBeginWithSlash.test(input.val());
      var invalidVar = reCheckVariable.test(input.val());
      if (!invalidSlash && !invalidVar) {
        $('#change-dl_dir-form').addClass('has-error');
        $("#apply-change-dl_dir").attr("disabled","disabled");
        $('#hintError-initialChar-dl_dir').show();
      } else if (invalidDir) {
        $('#change-dl_dir-form').addClass('has-error');
        $("#apply-change-dl_dir").attr("disabled","disabled");
        $('#hintError-dl_dir').show();
      } else {
        $('#change-dl_dir-form').removeClass('has-error');
        $("#apply-change-dl_dir").removeAttr("disabled");
        $('#hintError-dl_dir').hide();
        $('#hintError-initialChar-dl_dir').hide();
      }
    }
  });

  $('#apply-change-dl_dir').click(function(){
    var value = $('#new-dl_dir').val().trim();
    postEditAjaxRequest({"configvarChange" : 'DL_DIR:'+value});
    $('#dl_dir').text(value);
    $('#dl_dir').removeClass('muted');
    $("#change-dl_dir-form").slideUp(function () {
      $('#dl_dir, #change-dl_dir-icon').show();
    });
  });

  {% endif %}

  {% if fstypes_defined %}
  // change IMAGE_FSTYPES variable

  // get value of fstypes and add to the textbox
  $("#new-imagefs_types").val("{{fstypes}}");

  // If value of new-imagefs_types is empty disable save button
  $("#new-imagefs_types").on("input", function() {
    $(this).val($(this).val().replace(/\s+/g,' '));
    if ($(this).val().length === 0) {
      //$('#apply-change-image_fstypes').prop('disabled', true);
      $('#apply-change-image_fstypes').attr("disabled", "disabled");
    } else {
      //$('#apply-change-image_fstypes').prop('disabled', false);
      $('#apply-change-image_fstypes').removeAttr("disabled");
    }

    /*If user types imagefs do the action on checkboxes.
      Lets say if an imagefstype typed by user and the same
      imagefs is unchecked in the checkbox, then checkbox needs
      to get checked. Similarly when user deletes imagefs from
      textbox the checkbox which is checked gets unchecked.
     */
    $('#all-image_fstypes input').each(function(){
      var imagefs_userval = $('#new-imagefs_types').val();
      if( imagefs_userval.indexOf($(this).val()) > -1) {
        $(this).prop('checked', true);
      } else {
        $(this).prop('checked', false);
      }
    });

    // Validate underscore in image fs types
    if ($(this).val().indexOf('_') > -1) {
      $('#validate-image_fstypes').addClass('has-error');
      $('#hintError-image-fs_type').show();
      $("#apply-change-image_fstypes").prop("disabled", true);
    } else {
      $('#validate-image_fstypes').removeClass('has-error');
      $('#hintError-image-fs_type').hide();
    }
  });

  $('#change-image_fstypes-icon').click(function() {
    $('#change-image_fstypes-icon, #image_fstypes').hide();
    $("#change-image_fstypes-form").slideDown();
    // avoid false substring matches by including space separators
    var html         = "";
    var fstypes      = " " + document.getElementById("image_fstypes").innerHTML + " ";
    var fstypes_list = document.getElementsByClassName('js-checkbox-fstypes-list');
    // Add the checked boxes first
    if ("  " != fstypes) {
      for (var i = 0, length = fstypes_list.length; i < length; i++) {
        if (0 <= fstypes.indexOf(" "+fstypes_list[i].value+" ")) {
          html += '<div class="checkbox"><label><input type="checkbox" class="fs-checkbox-fstypes" value="'+fstypes_list[i].value+'" checked="checked">'+fstypes_list[i].value+'</label></div>';
        }
      }
    }
    // Add the un-checked boxes second
    for (var i = 0, length = fstypes_list.length; i < length; i++) {
      if (0  > fstypes.indexOf(" "+fstypes_list[i].value+" ")) {
        html += '<div class="checkbox"><label><input type="checkbox" class="fs-checkbox-fstypes" value="'+fstypes_list[i].value+'">'+fstypes_list[i].value+'</label></div>';
      }
    }
    // Add the 'no search matches' line last
    html += '<label id="no-match-fstypes" class="text-muted">No image types found</label>\n';
    // Display the list
    document.getElementById("all-image_fstypes").innerHTML = html;
    $('#no-match-fstypes').hide();

    // clear the previous filter values and warning messages
    $("input#filter-image_fstypes").val("");
  });

  // When checkbox is checked/unchecked kindly update the text
  $(document).on("change", "#all-image_fstypes :checkbox", function() {
    var imagefs = $(this);
    var imagefs_obj = $('#new-imagefs_types');
    var imagefs_userval = imagefs_obj.val();
    if ($(this).is(':checked')) {
      if (imagefs_userval.indexOf($(imagefs).val()) === -1) {
        imagefs_obj.val(imagefs_userval + " " + $(imagefs).val());
      }
    } else {
      if (imagefs_userval.indexOf($(imagefs).val()) > -1) {
        imagefs_obj.val(imagefs_userval.replace($(imagefs).val(), '').trim());
      }
    }
    if ($('#new-imagefs_types').val().length === 0) {
      $("#apply-change-image_fstypes").prop("disabled", true);
      $('#fstypes-error-message').show();
    } else {
      $("#apply-change-image_fstypes").prop("disabled", false);
      $('#fstypes-error-message').hide();
    }
  });

  $('#cancel-change-image_fstypes').click(function(){
    $("#new-imagefs_types").val("{{fstypes}}");
    $("#change-image_fstypes-form").slideUp(function() {
      $('#image_fstypes, #change-image_fstypes-icon').show();
    });
  });

  $('#filter-image_fstypes').on('input', function(){
    var valThis = $(this).val().toLowerCase();
    var matchCount=0;
    $('#all-image_fstypes label').each(function(){
      var text = $(this).text().toLowerCase();
      var match = text.indexOf(valThis);
      if (match >= 0) {
        $(this).show();
        matchCount += 1;
      }
      else {
        $(this).hide();
      }
    });
    if (matchCount === 0) {
      $('#no-match-fstypes').show();
    } else {
      $('#no-match-fstypes').hide();
    }
  });

  $('#apply-change-image_fstypes').click(function(){
    var fstypes = $('#new-imagefs_types').val();

    postEditAjaxRequest({"configvarChange" : 'IMAGE_FSTYPES:'+fstypes});
    $('#image_fstypes').text(fstypes);
    $('#image_fstypes').parent().removeClass('muted');

    $("#change-image_fstypes-form").slideUp(function() {
      $('#image_fstypes, #change-image_fstypes-icon').show();
    });
  });
  {% endif %}


  {% if image_install_append_defined %}

  // init IMAGE_INSTALL:append trash icon
  setDeleteTooltip($('#delete-image_install-icon'));

  // change IMAGE_INSTALL:append variable
  $('#change-image_install-icon').click(function() {
    // preset the edit value
    var current_val = $("span#image_install").text().trim();
    if (current_val == "Not set") {
      current_val="";
      $("#apply-change-image_install").attr("disabled","disabled");
    } else {
      // insure these non-empty values have single space prefix
      current_val=" " + current_val;
      $("#apply-change-image_install").removeAttr("disabled");
    }
    $("input#new-image_install").val(current_val);

    $('#change-image_install-icon, #delete-image_install-icon, #image_install').hide();
    $("#change-image_install-form").slideDown();
  });

  $('#cancel-change-image_install').click(function(){
    $("#change-image_install-form").slideUp(function() {
      $('#image_install, #change-image_install-icon').show();
      if ($("span#image_install").text() != "Not set") {
        $('#delete-image_install-icon').show();
        setDeleteTooltip($('#delete-image_install-icon'));
      }
    });
  });

  $("#new-image_install").on('input', function(){
    if ($(this).val().trim().length == 0) {
      $("#apply-change-image_install").attr("disabled","disabled");
    }
    else {
      $("#apply-change-image_install").removeAttr("disabled");
    }
  });

  $('#apply-change-image_install').click(function(){
    // insure these non-empty values have single space prefix
    var value = " " + $('#new-image_install').val().trim();
    postEditAjaxRequest({"configvarChange" : 'IMAGE_INSTALL:append:'+value});
    $('#image_install').text(value);
    $('#image_install').removeClass('text-muted');
    $("#change-image_install-form").slideUp(function () {
      $('#image_install, #change-image_install-icon').show();
      if (value.length > -1) {
        $('#delete-image_install-icon').show();
        setDeleteTooltip($('#delete-image_install-icon'));
      }
    });
  });

  // delete IMAGE_INSTALL:append variable value
  $('#delete-image_install-icon').click(function(){
    $(this).tooltip('hide');
    postEditAjaxRequest({"configvarChange" : 'IMAGE_INSTALL:append:'+''});
    $('#image_install').parent().fadeOut(1000, function(){
      $('#image_install').addClass('text-muted');
      $('#image_install').text('Not set');
      $('#delete-image_install-icon').hide();
      $('#image_install').parent().fadeIn(1000);
    });
  });
  {% endif %}


  {% if package_classes_defined %}
  // change PACKAGE_CLASSES variable
  $('#change-package_classes-icon').click(function() {
    $('#change-package_classes-icon, #package_classes').hide();
    $("#change-package_classes-form").slideDown();

    // initialize the pulldown and checkboxes
    var value = $("#package_classes").text();
    if ( value.indexOf("package_deb") == 0 ) {
      $("#package_classes-select").prop('selectedIndex', 0);
      updatePackageClassCheckboxes();
      if ( value.indexOf("_ipk") > 0 ) {
        $("#package_class_1_input").attr("checked",true);
      }
      if ( value.indexOf("_rpm") > 0 ) {
        $("#package_class_2_input").attr("checked",true);
      }
    }

    if ( value.indexOf("package_ipk") == 0 ) {
      $("#package_classes-select").prop('selectedIndex', 1);
      updatePackageClassCheckboxes();
      if ( value.indexOf("_deb") > 0 ) {
        $("#package_class_1_input").attr("checked",true);
      }
      if ( value.indexOf("_rpm") > 0 ) {
        $("#package_class_2_input").attr("checked",true);
      }
    }

    if ( value.indexOf("package_rpm") == 0 ) {
      $("#package_classes-select").prop('selectedIndex', 2);
      updatePackageClassCheckboxes();
      if ( value.indexOf("_deb") > 0 ) {
        $("#package_class_1_input").attr("checked",true);
      }
      if ( value.indexOf("_ipk") > 0 ) {
        $("#package_class_2_input").attr("checked",true);
      }
    }
  });

  $('#cancel-change-package_classes').click(function(){
    $("#change-package_classes-form").slideUp(function() {
      $('#package_classes, #change-package_classes-icon').show();
    });
  });

  $('select').change(function() {
    updatePackageClassCheckboxes();
  });

  $('#apply-change-package_classes').click(function(){
    var e   = document.getElementById("package_classes-select");
    var val = e.options[e.selectedIndex].text;

    pc1_checked = document.getElementById("package_class_1_input").checked;
    pc2_checked = document.getElementById("package_class_2_input").checked;
    if (val == "package_deb") {
      if (pc1_checked) val = val + " package_ipk";
      if (pc2_checked) val = val + " package_rpm";
    }
    if (val == "package_ipk") {
      if (pc1_checked) val = val + " package_deb";
      if (pc2_checked) val = val + " package_rpm";
    }
    if (val == "package_rpm") {
      if (pc1_checked) val = val + " package_deb";
      if (pc2_checked) val = val + " package_ipk";
    }

    $('#package_classes').text(val);
    //$('#package_classes').parent().removeClass('muted');
    postEditAjaxRequest({"configvarChange" : 'PACKAGE_CLASSES:'+val});
    $("#change-package_classes-form").slideUp(function() {
      $('#package_classes, #change-package_classes-icon').show();
    });
  });
  {% endif %}

  {% if sstate_dir_defined %}

  // change SSTATE_DIR variable
  $('#change-sstate_dir-icon').click(function() {
    $('#change-sstate_dir-form').removeClass('has-error');
    // preset the edit value
    var current_val = $("span#sstate_dir").text().trim();
    if (current_val == "Not set") {
      current_val="";
      $("#apply-change-sstate_dir").attr("disabled","disabled");
    }
    $("input#new-sstate_dir").val(current_val);

    // enable / disable the save button based on the input value
    if ( current_val.length ) {
      $("#apply-change-sstate_dir").removeAttr("disabled");
    }
    else {
      $("#apply-change-sstate_dir").attr("disabled","disabled");
    }

    $('#change-sstate_dir-icon, #sstate_dir').hide();
    $("#change-sstate_dir-form").slideDown();
  });

  $('#cancel-change-sstate_dir').click(function(){
    $("#hintError-sstate_dir").hide();
    $("#hintError-initialChar-sstate_dir").hide();
    $("#change-sstate_dir-form").slideUp(function() {
      $('#sstate_dir, #change-sstate_dir-icon').show();
    });
  });

  $("#new-sstate_dir").on('input', function(){
    if ($(this).val().trim().length == 0) {
      $("#apply-change-sstate_dir").attr("disabled","disabled");
      $('#change-sstate_dir-form').addClass('has-error');
      $('#hintError-sstate_dir').hide();
      $('#hintError-initialChar-sstate_dir').hide();
    }
    else {
      var input = $(this);
      var reBeginWithSlash = /^\//;
      var reCheckVariable = /^\$/;
      var re = /([ <>\\|":%\?\*]+)/;
      var invalidDir = re.test(input.val());
      var invalidSlash = reBeginWithSlash.test(input.val());
      var invalidVar = reCheckVariable.test(input.val());
      if (!invalidSlash && !invalidVar) {
        $('#change-sstate_dir-form').addClass('has-error');
        $("#apply-change-sstate_dir").attr("disabled","disabled");
        $('#hintError-initialChar-sstate_dir').show();
      } else if (invalidDir) {
          $('#change-sstate_dir-form').addClass('has-error');
          $("#apply-change-sstate_dir").attr("disabled","disabled");
          $('#hintError-sstate_dir').show();
      } else {
        $('#change-sstate_dir-form').removeClass('has-error');
        $("#apply-change-sstate_dir").removeAttr("disabled");
        $('#hintError-sstate_dir').hide();
        $('#hintError-initialChar-sstate_dir').hide();
      }
    }
  });

  $('#apply-change-sstate_dir').click(function(){
    var value = $('#new-sstate_dir').val().trim();
    postEditAjaxRequest({"configvarChange" : 'SSTATE_DIR:'+value});
    $('#sstate_dir').text(value);
    $('#sstate_dir').removeClass('text-muted');
    $("#change-sstate_dir-form").slideUp(function () {
      $('#sstate_dir, #change-sstate_dir-icon').show();
    });
  });

  {% endif %}

  // add new variable
  $("button#add-configvar-button").click( function (evt) {
    var variable = $("input#variable").val();
    var value    = $("input#value").val();

    postEditAjaxRequest({"configvarAdd" : variable+':'+value});

    // clear the previous values
    $("input#variable").val("");
    $("input#value").val("");
    // Disable add button
    $(".save").attr("disabled","disabled");

    // Reload page if admin-removed core managed value is manually added back in
    if (0 <= " DISTRO DL_DIR IMAGE_FSTYPES IMAGE_INSTALL:append PACKAGE_CLASSES SSTATE_DIR ".indexOf( " "+variable+" " )) {
      // delayed reload to avoid race condition with postEditAjaxRequest
      do_reload=true;
    }
  });

  // validate new variable name and value
  $("#variable, #value").on('input', function() {
    validate_new_variable();
  });

  //
  // draw and register the dynamic configuration variables and handlers
  //

  var data = {
    configvars : []
  };
  {% for c in configvars %}
  data.configvars.push([ "{{c.name}}","{{c.value}}","{{c.pk}}" ]);
  {% if '' != vars_context|get_dict_value:c.name %}
  data.vars_context[ "{{c.name}}" ] = "{{vars_context|get_dict_value:c.name }}";
  {% endif %}
  {% endfor %}

  // draw these elements and assert their event handlers
  onEditPageUpdate(data);
});

</script>

{% endblock %}
