<a class="btn btn-danger btn-block layer-exists-{{data.pk}} layerbtn"  data-layer='{
   "id": {{data.pk}},
   "name":  "{{data.layer.name}}",
   "xhrLayerUrl": "{% url "xhr_layer" extra.pid data.pk %}",
   "layerdetailurl": "{% url 'layerdetails' extra.pid data.pk %}"
   }' data-directive="remove"
    {% if data.pk not in extra.current_layers %}
    style="display:none;"
    {% endif %}
  >
  <span class="glyphicon glyphicon-trash"></span>
  Remove layer
</a>
<a class="btn btn-default btn-block layer-add-{{data.pk}} layerbtn"
   data-layer='{ "id": {{data.pk}},
   "name":  "{{data.layer.name}}",
   "xhrLayerUrl": "{% url "xhr_layer" extra.pid data.pk %}",
   "layerdetailurl": "{%url "layerdetails" extra.pid data.pk %}"
   }' data-directive="add"
    {% if data.pk in extra.current_layers %}
    style="display:none;"
    {% endif %}
  >
  <span class="glyphicon glyphicon-plus"></span>
  Add layer
</a>
