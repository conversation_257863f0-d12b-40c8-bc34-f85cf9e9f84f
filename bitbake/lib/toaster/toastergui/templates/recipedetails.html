{% extends "base.html" %}
{% load projecttags %}
{% load humanize %}
{% load static %}
{% block pagecontent %}

<ul class="breadcrumb">
  <li>
    <a href="{% url 'project' project.id %}">{{project.name}}</a>
    <span class="divider">&rarr;</span>
  </li>
  <li>
    {% if recipe.is_image %}
    <a href="{% url 'projectimagerecipes' project.id %}">Image recipes</a>
    {% else %}
    <a href="{% url 'projectsoftwarerecipes' project.id %}">Software recipes</a>
    {% endif %}
    <span class="divider">&rarr;</span>
  </li>
  <li class="active">
    {{recipe.name}} ({{recipe.layer_version.layer.name}})
  </li>
</ul>

<script src="{% static 'js/recipedetails.js' %}"></script>
<script>
  $(document).ready(function (){
    var ctx = {
      recipe : {
        id: {{recipe.pk}},
        name: "{{recipe.name}}",
        totalPackages: {{packages.count}},
        layer_version : {
         id: {{recipe.layer_version.pk}},
         name: "{{recipe.layer_version.layer.name}}",
         layerdetailurl: "{% url 'layerdetails' project.pk recipe.layer_version.pk %}"
        }
      }
    };

    try {
      recipeDetailsPageInit(ctx);
    } catch (e) {
      document.write("Sorry, An error has occurred loading this page");
      console.warn(e);
    }
  });
</script>

{% include 'newcustomimage_modal.html' %}

<div class="alert alert-success lead" id="image-created-notification" style="margin-top: 15px; display: none">
  <button type="button" data-dismiss="alert" class="close">x</button>
  Your custom image <strong>{{recipe.name}}</strong> has been created. You can now add or remove packages as needed.
</div>
<div class="page-header">
  <h1>
    {{recipe.name}}
    <small>({{recipe.layer_version.layer.name}})</small>
  </h1>
</div>

<div class="row">
  <div class="col-md-8">
    <div class="button-place btn-group" id="customise-build-btns"
        style="width: 100%;
      {% if not in_project %}
      display:none;
      {% endif %}">
      <button class="btn btn-default btn-lg build-recipe-btn" style="width: 50%">
        Build {{recipe.name}}
      </button>
      {% if recipe.is_image %}
      <button class="btn btn-default btn-lg customise-btn" data-recipe="{{recipe.pk}}" style="width: 50%">
        Customise {{recipe.name}}
      </button>
      {% endif %}
    </div>
    <div class="button-place">
      <button class="btn btn-default btn-block btn-lg" id="add-layer-btn"
          style="width:100%;
          {% if in_project %}
          display:none;
          {% endif %}">
        <i class="glyphicon glyphicon-plus"></i>
        Add the {{recipe.layer_version.layer.name}} layer to your project to build or customise this image recipe
      </button>
    </div>

    <div id="packages-table">
      {% if  packages.count %}
       {% url 'recipepackages' project.id recipe.id as xhr_table_url %}
       <h2>{{title}} (<span class="table-count-{{table_name}}">0</span>) </h2>
       {% include "toastertable.html" %}
      {% else %}
       <h2>{{title}}</h2>
      {% endif %}

      <div class="alert alert-info air" id="build-to-get-packages-msg"
      {# if there are packages and it's in the project don't show this msg #}
      {% if packages.count or not packages.count and not in_project %}
       style="display:none"
      {% endif %} >
        <p class="lead">Toaster has no package information for {{recipe.name}}. To generate package information, build {{recipe.name}}</p>
        <button class="btn btn-info btn-lg build-recipe-btn" style="margin:20px 0 10px 0;">Build {{recipe.name}}</button>
      </div>

      <div class="alert alert-info air" id="packages-alert"
      {% if packages.count or  in_project %}
      style="display:none"
      {% endif %}
      >
        <p class="lead">Toaster has no package information for {{recipe.name}}
        </p>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="well">
      <h2>About {{recipe.name}}</h2>
      <dl class="item-info">
        <dt>
        Approx. packages included
        <span class="glyphicon glyphicon-question-sign get-help" title="The number of packages included is based on information from previous builds and from parsing layers, so we can never be sure it is 100% accurate"></span>
        </dt>
        <dd class="no-packages">{{packages.count}}</dd>
        <dt>
        Approx. package size
        <span class="glyphicon glyphicon-question-sign get-help" title="Package size is based on information from previous builds, so we can never be sure it is 100% accurate"></span>
        </dt>
        <dd>{{approx_pkg_size.size__sum|filtered_filesizeformat}}</dd>
        {% if last_build %}
        <dt>Last build</dt>
        <dd>
        <span class="glyphicon glyphicon-ok-circle"></span>
        <a href="{% url 'projectbuilds' project.id%}">{{last_build.completed_on|date:"d/m/y H:i"}}</a>
        </dd>
        {% endif %}
        <dt>Recipe file</dt>
        <dd>
        <code>{{recipe.file_path|cut_path_prefix:recipe.layer_version.local_path}}</code>
        <a href="{{recipe.get_vcs_recipe_file_link_url}}"><span class="glyphicon glyphicon-new-window" title="View recipe file" data-toggle="tooltip"></span></a>
        </dd> 
        <dt>Layer</dt>
        <dd><a href="{% url 'layerdetails' project.id recipe.layer_version.pk %}">{{recipe.layer_version.layer.name}}</a></dd>
        <dt>
        Summary
        </dt>
        <dd>
        {{recipe.summary}}
        </dd>
        <dt>
        Description
        </dt>
        <dd>
        {{recipe.description}}
        </dd>
        <dt>Version</dt>
        <dd>
        {{recipe.version}}
        </dd>
        <dt>Section</dt>
        <dd>
        {{recipe.section}}
        </dd>
        <dt>License</dt>
        <dd>
        {{recipe.license}}
        </dd>
      </dl>
    </div>
  </div>
</div>

{% endblock %}
