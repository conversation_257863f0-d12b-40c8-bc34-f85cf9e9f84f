{% extends "basebuildpage.html" %}
{% load projecttags %}

{% block title %} Configuration summary - {{build.target_set.all|dictsort:"target"|join:", "}} {{build.machine}} - {{build.project.name}} - Toaster {% endblock %}
{% block localbreadcrumb %}
<li>Configuration</li>
{% endblock %}

{% block buildinfomain %}
<!-- page title -->
<div class="col-md-10">

 <div class="page-header build-data">
     <h1>Configuration</h1>
 </div>

<!-- configuration table -->
<div id="navTab">
<ul class="nav nav-tabs">
    <li class="active"><a href="#">Summary</a></li>
    <li class=""><a href="{% url 'configvars' build.id %}">BitBake variables</a></li>
</ul>

  <!-- summary -->
  <div id="summary" class="tab-pane active">
    <h3>Build configuration</h3>
    <dl class="dl-horizontal">
      {%if BB_VERSION %}<dt>BitBake version</dt><dd>{{BB_VERSION}}</dd> {% endif %}
      {%if BUILD_SYS %}<dt>Build system</dt><dd>{{BUILD_SYS}}</dd> {% endif %}
      {%if NATIVELSBSTRING %}<dt>Host distribution</dt><dd>{{NATIVELSBSTRING}}</dd> {% endif %}
      {%if TARGET_SYS %}<dt>Target system</dt><dd>{{TARGET_SYS}}</dd> {% endif %}
      {%if MACHINE %}<dt>Machine</dt><dd>{{MACHINE}}</dd> {% endif %}
      {%if DISTRO %}<dt>Distro</dt><dd>{{DISTRO}}</dd> {% endif %}
      {%if DISTRO_VERSION %}<dt>Distro version</dt><dd>{{DISTRO_VERSION}}</dd> {% endif %}
      {%if TUNE_FEATURES %}<dt>Tune features</dt><dd>{{TUNE_FEATURES}}</dd> {% endif %}
      {%if TARGET_FPU %}<dt>Target FPU</dt><dd>{{TARGET_FPU}}</dd> {% endif %}
      {%if targets.all %}<dt>Target(s)</dt>
          <dd> <ul class="list-unstyled"> {% for target in targets.all %}
               <li>{{target.target}}{%if forloop.counter > 1 %}<br>{% endif %}</li>
          {% endfor %} </ul> </dd> {% endif %}
    </dl>
    <h3>Layers</h3>
    <div class="row">
      <div class="col-md-9 table-responsive">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>Layer</th>
              <th>Layer branch</th>
              <th>Layer commit</th>
            </tr>
          </thead>
          <tbody>{% for lv in build.layer_version_build.all|dictsort:"layer.name" %}
          <tr>
            <td>{{lv.layer.name}}</td>
	    {% if lv.layer.local_source_dir %}
	    <td>
	      <span class="text-muted">Not applicable</span>
	      <span class="glyphicon glyphicon-question-sign get-help" data-original-title="" title="The source code of {{lv.layer.name}} is not in a Git repository, so there is no branch associated with it"> </span>
	    </td>
	    {% else %}
            <td>{{lv.branch}}</td>
	    {% endif %}
	    {% if lv.layer.local_source_dir %}
	    <td>
	      <span class="text-muted">Not applicable</span>
	      <span class="glyphicon glyphicon-question-sign get-help" data-original-title="" title="The source code of {{lv.layer.name}} is not in a Git repository, so there is no commit associated with it"> </span>
	    </td>
	    {% else %}
            <td> <a class="btn btn-default" data-content="<ul class='list-unstyled'>
                  <li>{{lv.commit}}</li> </ul>">
                {{lv.commit|truncatechars:13}}
            </a></td>
	    {% endif %}
          </tr>{% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

</div>

</div> <!-- end of 10-column section -->
{% endblock %}
