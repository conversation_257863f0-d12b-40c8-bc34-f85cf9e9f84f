{% extends "base.html" %}

{% load static %}
{% load projecttags %}
{% load humanize %}

{% block title %} Welcome to Toaster {% endblock %}
{% block pagecontent %}
      <div class="row">
        <div class="jumbotron well-transparent">

            <div class="col-md-6">
              <h1>This is Toaster</h1>

              <p>A web interface to <a href="https://www.openembedded.org">OpenEmbedded</a> and <a href="https://www.yoctoproject.org/tools-resources/projects/bitbake">BitBake</a>, the <a href="https://www.yoctoproject.org">Yocto Project</a> build system.</p>

		          <p class="top-air">
		            <a class="btn btn-info btn-lg" href="http://docs.yoctoproject.org/toaster-manual/setup-and-use.html#setting-up-and-using-toaster">
			            Toaster is ready to capture your command line builds
		            </a>
		          </p>

		          {% if lvs_nos %}
                    {% if project_enable %}
		            <p class="top-air">
		              <a class="btn btn-primary btn-lg" href="{% url 'newproject' %}">
			              Create your first Toaster project to run manage builds
		              </a>
		            </p>
                    {% endif %}
		          {% else %}
                <div class="alert alert-info lead top-air">
                  Toaster has no layer information. Without layer information, you cannot run builds. To generate layer information you can:
                  <ul>
                    <li>
                      <a href="http://docs.yoctoproject.org/toaster-manual/reference.html#layer-source">Configure a layer source</a>
                    </li>
                    <li>
			                <a href="{% url 'newproject' %}">Create a project</a>, then import layers
                    </li>
                  </ul>
                </div>
              {% endif %}

              <ul class="list-unstyled lead">
                <li>
                  <a href="http://docs.yoctoproject.org/toaster-manual/index.html#toaster-user-manual">
                    Read the Toaster manual
                  </a>
                </li>

                <li>
                  <a href="https://wiki.yoctoproject.org/wiki/Contribute_to_Toaster">
                    Contribute to Toaster
                  </a>
                </li>
              </ul>
            </div>

            <div class="col-md-6">
              <img alt="Yocto Project Toaster" class="img-thumbnail" src="{% static 'img/toaster_bw.png' %}"/>
            </div>

        </div>
      </div>

{% endblock %}
