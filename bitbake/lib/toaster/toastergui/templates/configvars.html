{% extends "basebuildpage.html" %}
{% load projecttags %}

{% block title %} BitBake variables - {{build.target_set.all|dictsort:"target"|join:", "}} {{build.machine}} - {{build.project.name}} - Toaster {% endblock %}
{% block localbreadcrumb %}
<li>Configuration</li>
{% endblock %}

{% block buildinfomain %}
<!-- page title -->
<div class="col-md-10">
 <div class="page-header build-data">
 <h1>
  {% if request.GET.filter and objects.paginator.count > 0 or request.GET.search and objects.paginator.count > 0 %}
      {{objects.paginator.count}} variable{{objects.paginator.count|pluralize}} found
  {%elif request.GET.filter and objects.paginator.count == 0 or request.GET.search and objects.paginator.count == 0 %}
      No variables found
  {%else%}
      Configuration
  {%endif%}
 </h1>
 </div>

<!-- configuration table -->
<div id="navTab">
  <ul class="nav nav-tabs">
    <li class=""><a href="{% url 'configuration' build.id %}">Summary</a></li>
    <li class="active"><a href="#" >BitBake variables</a></li>
  </ul>

  <!-- variables -->
  <div id="variables" class="tab-pane">

  {% if objects.paginator.count == 0 %}
  <div class="alert alert-warning">
    <form class="no-results form-inline" id="searchform">
      <div class="form-group">
        <div class="btn-group">
          <input class="form-control" id="search" name="search" type="text" value="{% if request.GET.search %}{{request.GET.search}}{% endif %}"/>
          {% if request.GET.search %}
          <a href="javascript:$('#search').val('');searchform.submit()" tabindex="-1">
            <span class="remove-search-btn-variables glyphicon glyphicon-remove-circle"></span>
          </a>
          {% endif %}
        </div>
      </div>
      <button class="btn btn-default" type="submit" value="Search">Search</button>
      <button class="btn btn-link" onclick="javascript:$('#search').val('');searchform.submit()">Show all variables</button>
    </form>
  </div>

  {% else %}
  {% include "basetable_top.html" %}

  {% for variable in objects %}
    <tr class="data">
        <td class="variable_name"><a data-toggle="modal" href="#variable-{{variable.pk}}">{{variable.variable_name}}</a></td>
        <td class="variable_value">{{variable.variable_value|truncatechars:153}}</td>
        <td class="file">
            {% if variable.vhistory.all %}
                {% for path in variable.vhistory.all|filter_setin_files:file_filter %}
                    {{path}}<br/>
                {% endfor %}
            {% endif %}
        </td>
        <td class="description">
            {% if variable.description %}
                {{variable.description}}
                <a href="http://docs.yoctoproject.org/ref-manual/variables.html#term-{{variable.variable_name|variable_parent_name}}" target="_blank">
                <span class="glyphicon glyphicon-new-window get-info"></span></a>
            {% endif %}
        </td>
    </tr>
{% endfor %}
</tbody>
</table>
</div> <!-- table-responsive -->
{% include "basetable_bottom.html" %}
{% endif %}
</div> <!-- endvariables -->

<!-- file list popups -->
{% for variable in objects %}
{% if variable.vhistory.count %}
<div id="variable-{{variable.pk}}" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">x</button>
        <h3>History of {{variable.variable_name}}</h3>
      </div>
      <div class="modal-body">
        {% if variable.variable_value %}
        {% if variable.variable_value|length < 570 %}
        <h4>{{variable.variable_name}} value is:</h4>
        <p>
        {{variable.variable_value}}
        </p>
        {% else %}
        <h4>{{variable.variable_name}} value is:</h4>
        <p>
        <span>{{variable.variable_value|string_slice:':570'}}
          <span class="full"> {{variable.variable_value|string_slice:'570:'}}
          </span>
          <a href="#" class="full-show">...</a>
        </span>
        </p>
        <a href="#" class="full-hide">Collapse variable value <i class="icon-caret-up"></i>
        </a>
        {% endif %}
        {% else %}
        <div class="alert alert-info">The value of <strong>{{variable.variable_name}}</strong> is an empty string</div>
        {% endif %}
        <h4>The value was set in the following configuration files:</h4>
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>Order</th>
                <th>Configuration file</th>
                <th>Operation</th>
                <th>Line</th>
              </tr>
            </thead>
            <tbody>
              {% for vh in variable.vhistory.all %}
              <tr>
                <td>{{forloop.counter}}</td><td class="file">{{vh.file_name}}</td><td>{{vh.operation}}</td><td>{{vh.line_number}}</td>
              </tr>
              {%endfor%}
            </tbody>
          </table>
        </div>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
{% endif %}
{% endfor %}

</div> <!-- buildinfomain -->

</div> <!-- end 10-column section -->

{% endblock %}
