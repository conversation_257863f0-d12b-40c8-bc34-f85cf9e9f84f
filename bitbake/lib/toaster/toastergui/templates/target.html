{% extends "basebuildpage.html" %}
{% block title %} Packages included - {{ target.target }} {{ target.build.machine }} - {{ target.build.project.name }} - Toaster {% endblock %}
{% block localbreadcrumb %}
<li>{{target.target}}</li>
{% endblock localbreadcrumb%}

{% load projecttags %}

{% block nav-target %}
  {% for t in build.get_sorted_target_list %}
    {% ifequal target.pk t.pk %}
      <li class="active"><a href="{% url 'target' build.pk t.pk %}">{{t.target}}</a><li>
    {% else %}
      <li><a href="{% url 'target' build.pk t.pk %}">{{t.target}}</a><li>
    {% endifequal %}
  {% endfor %}
{% endblock %}

{% block buildinfomain %}
<div class="col-md-10">
    <div class="page-header build-data">
        <h1>
            {% if request.GET.search and objects.paginator.count > 0 %}
                {{objects.paginator.count}} package{{objects.paginator.count|pluralize}} found
            {% elif request.GET.search and objects.paginator.count == 0 %}
                No packages found
            {% else %}
                {{target.target}}
            {% endif %}
        </h1>
    </div>
<div id="navTab">
    <ul class="nav nav-tabs">
        <li class="active">
            <a href="#target">
            <span class="glyphicon glyphicon-question-sign get-help" title="Of all the packages built, the subset installed in the root file system of this image"></span>
                Packages included ({{target.package_count}} - {{packages_sum|filtered_filesizeformat}})
            </a>
        </li>
        <li>
            <a href="{% url 'dirinfo' build.id target.id %}">
                <span class="glyphicon glyphicon-question-sign get-help" title="The directories and files in the root file system of this image"></span>
                Directory structure
            </a>
        </li>
    </ul>

    <div id="image-packages" class="tab-pane">
        {# xhr_table_url is just the current url so leave it blank #}
        {% with xhr_table_url='' %}
        {% include "toastertable.html" %}
        {% endwith %}
    </div> <!-- tabpane -->
	</div> <!--navTab -->
<!-- col-md-10 -->
{% endblock buildinfomain %}
