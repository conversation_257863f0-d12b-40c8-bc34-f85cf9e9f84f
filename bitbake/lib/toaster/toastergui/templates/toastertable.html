
{% load static %}
{% load projecttags %}

<script src="{% static 'js/table.js' %}"></script>
<script src="{% static 'js/layerBtn.js' %}"></script>
<script>
  $(document).ready(function() {
    (function(){

    var ctx = {
      tableName : "{{table_name}}",
      url : "{{ xhr_table_url }}?format=json",
      title : "{{title}}",
    };

    try {
      tableInit(ctx);
    } catch (e) {
      document.write("Problem loading table widget: " + e);
    }
    })();
  });
</script>

{% include 'toastertable-filter.html' %}

<div class="row-fluid" id="empty-state-{{table_name}}" style="display:none">
  <div class="alert alert-info">{{empty_state|safe}}</div>
</div>

<div id="no-results-{{table_name}}" style="display:none">
  <div class="alert alert-warning">
    <form class="form-inline">
      <div class="form-group">
        <div class="btn-group">
          <input class="form-control" id="new-search-input-{{table_name}}" name="search" type="text" placeholder="Search {{title|lower}}" value="{%if request.GET.search %}{{request.GET.search}}{%endif%}"/>
          <span class="remove-search-btn-{{table_name}} glyphicon glyphicon-remove-circle" tabindex="-1"></a>
        </div>
      </div>
      <button class="btn btn-default search-submit-{{table_name}}">
        Search
      </button>
      <button class="btn btn-link show-all-{{table_name}} remove-search-btn-{{table_name}}">
        Show all
      </button>
    </form>
  </div>
</div>

<div id="table-container-{{table_name}}" style="visibility: hidden">
  <!-- control header -->
  <div class="navbar navbar-default" id="table-chrome-{{table_name}}">
    <div class="container-fluid">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#table-chrome-collapse-{{table_name}}" aria-expanded="false">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
      </div>
      <div class="collapse navbar-collapse" id="table-chrome-collapse-{{table_name}}">
        <form class="navbar-form navbar-left">
          <div class="form-group">
            <div class="btn-group">
              <input id="search-input-{{table_name}}" class="form-control" name="search" type="text" placeholder="Search {{title|lower}}" value="{%if request.GET.search%}{{request.GET.search}}{%endif%}"/>
              <span class="remove-search-btn-{{table_name}} glyphicon glyphicon-remove-circle" tabindex="-1" style="display:none;">
            </div>
          </div>
          <button class="btn btn-default" id="search-submit-{{table_name}}" >Search</button>
        </form>
        <form class="navbar-form navbar-right">
          <div clas="form-group">
            <label>Show rows:</label>
            <select class="form-control pagesize-{{table_name}}">
              {% with "10 25 50 100 150" as list%}
              {% for i in list.split %}
              <option value="{{i}}">{{i}}</option>
              {% endfor %}
              {% endwith %}
            </select>
          </div>
        </form>
        <div class="btn-group navbar-right">
          <button id="edit-columns-button" class="btn btn-default navbar-btn dropdown-toggle" data-toggle="dropdown">Edit columns
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu editcol">
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- The actual table -->
  <div class="table-responsive">
    <table class="table table-bordered table-hover" id="{{table_name}}">
      <thead>
        <tr><th></th></tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <!-- Pagination controls -->
  <div id="pagination-{{table_name}}">
    <ul class="pagination">
    </ul>

    <form class="navbar-form navbar-right">
      <div class="form-group">
        <label>Show rows:</label>
        <select class="form-control pagesize-{{table_name}}">
          {% with "10 25 50 100 150" as list%}
          {% for i in list.split %}
          <option value="{{i}}">{{i}}</option>
          {% endfor %}
          {% endwith %}
        </select>
      </div>
    </form>
  </div>

</div> <!--end table container -->
