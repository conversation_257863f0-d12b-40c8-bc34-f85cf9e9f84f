{% extends "basebuilddetailpage.html" %}

{% load projecttags %}
{% load humanize %}
{% block localbreadcrumb %}
<li><a href="{% url 'recipes' build.pk %}">Recipes</a></li>
<li>{{recipe.name}}_{{recipe.version}} </li>
{% endblock %}

{% block pagedetailinfomain %}

<!-- Begin container -->

<div class="row">
  <div class="col-md-12">
    <div class="page-header build-data">
        <h1>{{recipe.name}}_{{recipe.version}}</h1>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-8 tabbable">
    <ul class="nav nav-tabs">
        <li>
            <a href="{% url "recipe" build.pk recipe.id "1" %}">
                <span class="glyphicon glyphicon-question-sign get-help" title="Build-related
                    information about the recipe"></span>
                Recipe details
            </a>
        </li>
        <li class="active">
          <a href="#packages-built" data-toggle="tab">
                <span class="glyphicon glyphicon-question-sign get-help" title="The packaged
                    output resulting from building the recipe"></span>
                Packages ({{object_count}})
            </a>
        </li>
        <li>
            <a href="{% url "recipe" build.pk recipe.id "3" %}">
                <span class="glyphicon glyphicon-question-sign get-help" title="The recipe
                    build-time dependencies (i.e. other recipes)"></span>
                Build dependencies ({{recipe.r_dependencies_recipe.all.count}})
            </a>
        </li>
        <li>
            <a href="{% url "recipe" build.pk recipe.id "4" %}">
                <span class="glyphicon glyphicon-question-sign get-help" title="The recipe
                    build-time reverse dependencies (i.e. the recipes that
                    depend on this recipe)"></span>
                Reverse build dependencies ({{recipe.r_dependencies_depends.all.count}})
            </a>
        </li>
    </ul>
    <div class="tab-content">
{#        <div class="tab-pane active" id="packages-built" name="packages-built">#}
       <div class="tab-pane active" id="packages-built">
            {% if not objects and not request.GET.search %}
            <div class="alert alert-info">
                <strong>{{recipe.name}}_{{recipe.version}}</strong> does not build any packages.
            </div>

            {% elif not objects %}
                {# have empty search results, no table nor pagination #}
                {% with "packages" as search_what %}
                {% include "detail_search_header.html" %}
                {% endwith %}

            {% else %}


                {% with "packages" as search_what %}
                {% include "detail_search_header.html" %}
                {% endwith %}
            <table class="table table-bordered table-hover tablesorter" id="otable">
                {% include "detail_sorted_header.html" %}

                <tbody>
                    {% for package in objects %}

                    <tr>
                        <td><a href="{% url "package_built_detail" build.pk package.pk %}">{{package.name}}</a></td>
                        <td>{{package.version}}_{{package.revision}}</td>
                        <td class="sizecol">{{package.size|filtered_filesizeformat}}</td>
                    </tr>

                        {% endfor %}

            {% endif %}
            {% if objects %}
                </tbody>
            </table>
                {% include "detail_pagination_bottom.html" %}
            {% endif %}
        </div> {# tab-pane #}
    </div> {# tab-content #}
</div> {# col-md-8 #}

<div class="col-md-4">
  <div class="well">
    <h2>About {{recipe.name}}</h2>
    <dl class="item-info">
        {% if recipe.summary %}
            <dt>Summary</dt>
            <dd>{{recipe.summary}}</dd>
        {% endif %}
        {% if recipe.description %}
            <dt>Description</dt>
            <dd>{{recipe.description}}</dd>
        {% endif %}
        {% if recipe.homepage %}
            <dt>Homepage</dt>
            <dd><a href="{{recipe.homepage}}">{{recipe.homepage}}</a></dd>
        {% endif %}
        {% if recipe.bugtracker %}
            <dt>Bugtracker</dt>
            <dd><a href="{{recipe.bugtracker}}">{{recipe.bugtracker}}</a></dd>
        {% endif %}
        {% if recipe.section %}
            <dt>
            Section
            <i class="icon-question-sign get-help" title="The section in which recipes should be categorized"></i>
            </dt>
            <dd>{{recipe.section}}</dd>
        {% endif %}
        {% if recipe.license %}
            <dt>License</dt>
            <dd>{{recipe.license}}</dd>
        {% endif %}
    </dl>
  </div>
</div>

</div> <!-- end row -->
{% endblock pagedetailinfomain %}
