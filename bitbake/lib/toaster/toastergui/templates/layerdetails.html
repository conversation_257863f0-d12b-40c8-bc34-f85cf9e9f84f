{% extends project_specific|yesno:"baseprojectspecificpage.html,base.html" %}
{% load projecttags %}
{% load humanize %}
{% load static %}

{% block title %} {{layerversion.layer.name}} - {{project.name}} - Toaster {% endblock %}
{% block pagecontent %}

<div id="delete-layer-modal" class="modal fade" tabindex="-1" role="dialog"
    data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-body">
		  Are you sure you want to delete the <strong>{{layerversion.layer.name}}</strong> layer?
      </div>
      <div class="modal-footer">
        <button type="button" id="layer-delete-confirmed" class="btn
            btn-primary">Delete layer</button>
        <button type="button" class="btn btn-default btn-link" data-dismiss="modal">Cancel</button>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <ul class="breadcrumb">
      <li>
        <a href="{% url 'project' project.id %}">{{project.name}}</a>
        <span class="divider">&rarr;</span>
      </li>
      <li><a href="{% url 'projectlayers' project.id %}">Compatible layers</a>
        <span class="divider">&rarr;</span>
      </li>
      <li class="active">
        {{layerversion.layer.name}} ({{layerversion.get_vcs_reference|truncatechars:13}})
      </li>
    </ul>

    {# If this is not an imported layer then hide the edit ui #}
    {% if layerversion.layer_source != layer_source.TYPE_IMPORTED %}
    <style scoped>
    .glyphicon-edit {
      display:none;
    }
    .delete-current-value{
      display: none;
    }
    li .glyphicon-trash {
      display:none;
    }
    .add-deps {
     display:none;
    }
    </style>
    {% endif %}

    <script src="{% static 'js/layerdetails.js' %}"></script>
    <script>

    $(document).ready(function(){
      var ctx = {
        xhrUpdateLayerUrl : "{% url 'xhr_layer' project.id layerversion.pk %}",
        layerVersion : {
          name : "{{layerversion.layer.name}}",
          id : {{layerversion.id}},
          commit: "{{layerversion.get_vcs_reference}}",
          {%if layerversion.id in projectlayers %}
          inCurrentPrj : true,
          {% else %}
          inCurrentPrj : false,
          {% endif %}
          layerdetailurl : "{% url 'layerdetails' project.id layerversion.id  %}",
          xhrLayerUrl: "{% url 'xhr_layer' project.id layerversion.id  %}",
          layer_source: {{layerversion.layer_source|json}},
        },
        layerSourceTypes: {{layer_source|json}},
      };

      try {
        layerDetailsPageInit(ctx);
      } catch (e) {
        document.write("Sorry, An error has occurred loading this page");
        console.warn(e);
      }
    });
    </script>

    <div class="page-header">
      {% if layerversion.layer.local_source_dir %}
      <h1>{{layerversion.layer.name}} <small class="commit" style="display:none;"></small>
      </h1>
      {% else %}
      <h1>{{layerversion.layer.name}} <small class="commit"
        {% if layerversion.get_vcs_reference|length > 13 %}
           data-toggle="tooltip" title="{{layerversion.get_vcs_reference}}"
        {% endif %}>({{layerversion.get_vcs_reference|truncatechars:13}})</small>
      </h1>
      {% endif %}
    </div>
    <div class="row">
      <!-- container for tabs -->
      <div class="col-md-8 tabbable">
        <div class="alert alert-info lead" id="alert-area" style="display:none">
          <button type="button" class="close" id="dismiss-alert">&times;</button>
          <span id="alert-msg"></span>
        </div>

        {% if layerversion.id not in projectlayers %}
         <button id="add-remove-layer-btn" data-directive="add" class="btn btn-default btn-lg btn-block">
           <span class="glyphicon glyphicon-plus"></span>
           Add the {{layerversion.layer.name}} layer to your project
         </button>
         {% else %}
         <button id="add-remove-layer-btn" data-directive="remove" class="btn btn-default btn-block btn-lg btn-danger">
           <span class="glyphicon glyphicon-trash"></span>
           Remove the {{layerversion.layer.name}} layer from your project
         </button>
         {% endif %}

        <ul class="nav nav-tabs">
          <li class="active">
            <a data-toggle="tab" href="#information" id="details-tab">Layer details</a>
          </li>
          <li>
            <a data-toggle="tab" href="#recipes" class="text-muted" id="targets-tab">Recipes (<span class="table-count-recipestable"></span>)</a>
          </li>
          <li>
            <a data-toggle="tab" href="#machines" class="text-muted" id="machines-tab">Machines (<span class="table-count-machinestable"></span>)</a>
          </li>
        </ul>
        <div class="tab-content">

          <!-- layer details pane -->
          <div id="information" class="tab-pane active">
           <h3>Layer source code location</h3>
           {% if layerversion.layer.local_source_dir %}
           <dl class="dl-horizontal" id="directory-info">
             <dt>
               Path to the layer directory
             </dt>
             <dd>
               <code>{{layerversion.layer.local_source_dir}}</code>
             </dd>
           </dl>
           {% else %}
            <dl class="dl-horizontal" id="git-repo-info">
              <dt class="">
              <span class="glyphicon glyphicon-question-sign get-help" title="Fetch/clone URL of the repository"></span>
              Repository URL
              </dt>
              <dd>
              <span class="current-value">{{layerversion.layer.vcs_url}}</span>
              {% if layerversion.get_vcs_link_url %}
              <a href="{{layerversion.get_vcs_link_url}}/" class="glyphicon glyphicon-new-window" target="_blank"></a>
              {% endif %}
              <form id="change-repo-form" class="form-inline" style="display:none">
                <div class="form-group">
                  <input type="text" class="form-control" value="{{layerversion.layer.vcs_url}}">
                </div>
              </form>
              </dd>
             {% if layerversion.dirpath %}
              <dt>
              <span class="glyphicon glyphicon-question-sign get-help" title="Subdirectory within the repository where the layer is located, if not in the root (usually only used if the repository contains more than one layer)"></span>
              Repository subdirectory
              </dt>
              <dd>
              <span class="text-muted" style="display:none">Not set</span>
              <span class="current-value">{{layerversion.dirpath}}</span>
              {% if layerversion.get_vcs_dirpath_link_url %}
              <a href="{{layerversion.get_vcs_dirpath_link_url}}" class="glyphicon glyphicon-new-window" target="_blank"></a>
              {% endif %}
              <form id="change-subdir-form" class="form-inline" style="display:none;">
                <div class="form-group">
                  <input type="text" class="form-control" value="{{layerversion.dirpath}}">
                </div>
              </form>
              </dd>
              {% endif %}
              <dt>
              <span class="glyphicon glyphicon-question-sign get-help" title="The Git branch, tag or commit"></span>
              Git revision
              </dt>
              <dd>
              <span class="current-value">{{layerversion.get_vcs_reference}}</span>
              <form style="display:none;" class="form-inline">
                <div class="form-group">
                  <input type="text" class="form-control" value="{{layerversion.get_vcs_reference}}">
                </div>
              </form>
              </dd>
            </dl>
            {% endif %}
            {% if layerversion.layer_source == layer_source.TYPE_IMPORTED %}
            <button class="btn btn-default btn-lg" id="edit-layer-source" style="margin-left:220px;">Edit layer source code location</button>
            {% endif %}
           <form id="edit-layer-source-form" style="display:none;">
             <fieldset>
               <legend class="radioLegend">Where is the layer source code?</legend>
               <div class="radio">
                 <label>
                   <input type="radio" name="source-location" id="repo" value="repo">
                   In a <strong>Git repository</strong>
                 </label>
                 <p class="help-block" style="margin-left:20px;width:70%;">To build the layer Toaster must be able to access the Git repository, otherwise builds will fail. Toaster will fetch and checkout your chosen Git revision every time you start a build.</p>
               </div>
               <div class="radio" style="margin-top:15px;">
                 <label>
                   <input type="radio" name="source-location" id="dir" value="dir" checked>
                   In a <strong>directory</strong>
                 </label>
                 <p class="help-block" style="margin-left:20px;width:70%;">Use this option for quick layer development, by simply providing the path to the layer source code.</p>
               </div>
             </fieldset>

             <fieldset id="layer-git">
               <legend>Git repository information</legend>
               <div class="form-group">
                 <label for="layer-git-repo-url">
                   Git repository URL
                   <span class="glyphicon glyphicon-question-sign get-help" title="Fetch/clone URL of the repository. Currently, Toaster only supports Git repositories." ></span>
                 </label>
                 <input type="text" id="layer-git-repo-url" class="form-control" value="{{layerversion.layer.vcs_url|default_if_none:''}}">
               </div>
               <div class="form-group">
                 <label for="layer-subdir">
                   Repository subdirectory
                   <span class="text-muted">(optional)</span>
                   <span class="glyphicon glyphicon-question-sign get-help" title="Subdirectory within the repository where the layer is located, if not in the root (usually only used if the repository contains more than one layer)"></span>
                 </label>
                 <input type="text" class="form-control" id="layer-subdir" value="{{layerversion.dirpath|default_if_none:''}}">
               </div>
               <div class="form-group" id="layer-revision-ctrl">
                 <label for="layer-git-ref">Git revision
                   <span class="glyphicon glyphicon-question-sign get-help" title="You can provide a Git branch, a tag or a commit SHA as the revision"></span>
                 </label>
                 <input type="text" class="form-control" id="layer-git-ref" value="{{layerversion.get_vcs_reference|default_if_none:''}}">
                 <span class="help-inline" style="display:none;" id="invalid-layer-revision-hint"></span>
               </div>
             </fieldset>

             <fieldset id="layer-dir">
               <legend>Layer directory information</legend>
               <div class="form-group">
                 <label for="layer-dir-path">
                   Enter the absolute path to the layer directory
                 </label>
                 <input type="text" id="layer-dir-path-in-details" class="form-control" value="{{layerversion.layer.local_source_dir}}" required>
               </div>
             </fieldset>

             <div style="margin-top:25px;">
               <a href="#" class="btn btn-primary btn-lg" id="save-changes-for-switch">Save changes</a>
               <a href="#" class="btn btn-link btn-lg" id="cancel-changes-for-switch">Cancel</a>
             </div>
           </form>

           <h3 class="top-air">Layer dependencies
             <span class="glyphicon glyphicon-question-sign get-help" title="Other layers this layer depends upon"></span>
           </h3>

           <ul class="list-unstyled current-value lead" id="layer-deps-list">
             {% for ld in layerversion.dependencies.all %}
             <li data-layer-id="{{ld.depends_on.id}}">
             <a data-toggle="tooltip" title="{{ld.depends_on.layer.vcs_url}} | {{ld.depends_on.get_vcs_reference}}" href="{% url 'layerdetails' project.id ld.depends_on.id %}">{{ld.depends_on.layer.name}}</a>
             <span class="glyphicon glyphicon-trash " data-toggle="tooltip" title="Delete"></span>
             </li>
             {% endfor %}
           </ul>
           <form class="form-inline add-deps">
             <div class="form-group">
               <input class="form-control" type="text" autocomplete="off" data-minLength="1" data-autocomplete="off"  placeholder="Type a layer name" id="layer-dep-input">
             </div>
             <a class="btn btn-default" id="add-layer-dependency-btn" disabled="disabled">
               Add layer
             </a>
             <span class="help-block add-deps">You can only add layers Toaster knows about</span>
           </form>
         </div>
          <!-- end layerdetails tab -->
          <!-- targets tab -->
          <div id="recipes" class="tab-pane">
            <!-- Recipe table -->
            <div id="no-recipes-yet" class="alert alert-info" style="display:none">
              <p>Toaster does not have recipe information for the <strong> {{layerversion.layer.name}} </strong> layer.</p>
              <p>Toaster learns about layers when you build them. If this layer provides any recipes, they will be listed here after you build the <strong> {{layerversion.layer.name}} </strong> layer.</p>
            </div>
            {% url 'layerrecipestable' project.id layerversion.id  as xhr_table_url %}
            {% with "recipestable" as table_name %}
            {% with "Recipes" as title %}
            {% include 'toastertable-simple.html' %}
            {% endwith %}
            {% endwith %}
          </div>

          <div id="machines" class="tab-pane">

            <div id="no-machines-yet" class="alert alert-info" style="display:none">
              <p>Toaster does not have machine information for the <strong> {{layerversion.layer.name}} </strong> layer.</p>
              <p>Sadly, machine information cannot be obtained from builds, so this page will remain empty.</p>
            </div>


            <!-- Machines table -->
            {% url 'layermachinestable' project.id layerversion.id  as xhr_table_url %}
            {% with "machinestable" as table_name  %}
            {% with "Machines" as title  %}
            {% include 'toastertable-simple.html' %}
            {% endwith %}
            {% endwith %}
          </div>

        </div> <!-- end tab content -->
      </div> <!-- end tabable -->

      <div class="col-md-4"> <!-- info side panel -->
        <div class="well">
          <h2>About {{layerversion.layer.name}}</h2>
          <dl class="item-info">

            <dt>
            Summary
            <span class="glyphicon glyphicon-question-sign get-help" title="One-line description of the layer"></span>
            </dt>
            <dd>
            <span class="text-muted" style="display:none">Not set</span>
            <span class="current-value">{{layerversion.layer.summary|default_if_none:''}}</span>
            <form style="display:none; margin-bottom:20px; margin-top:5px;">
              <div class="form-group">
                <textarea class="form-control" rows="2">{% if layerversion.layer.summary %}{{layerversion.layer.summary}}{% endif %}</textarea>
              </div>
              <button class="btn btn-default change-btn" data-layer-prop="summary" type="button">Save</button>
              <a href="#" class="btn btn-link cancel">Cancel</a>
            </form>
            <span class="glyphicon glyphicon-edit"></span>
            <span class="glyphicon glyphicon-trash delete-current-value" data-toggle="tooltip" title="Delete"></span>
            </dd>
            <dt>
            Description
            </dt>
            <dd>
            <span class="text-muted" style="display:none">Not set</span>
            <span class="current-value">{{layerversion.layer.description|default_if_none:''}}</span>
            <form style="display:none; margin-bottom:20px; margin-top:5px;">
              <div class="form-group">
                <textarea class="form-control" rows="6">{% if layerversion.layer.description %}{{layerversion.layer.description}}{% endif %}</textarea>
              </div>
              <button class="btn btn-default change-btn" data-layer-prop="description" type="button" >Save</button>
              <a href="#" class="btn btn-link cancel">Cancel</a>
            </form>
            <span class="glyphicon glyphicon-edit"></span>
            <span class="glyphicon glyphicon-trash delete-current-value" data-toggle="tooltip" title="Delete"></span>
            </dd>
            {% if layerversion.layer_source == layer_source.TYPE_LAYERINDEX  %}
            <dt>Layer index</dt>
            <dd>
            <a href="https://layers.openembedded.org/layerindex/branch/{{layerversion.release.name}}/layer/{{layerversion.layer.name}}">Layer index {{layerversion.layer.name}}</a>
            </dd>
            {% endif %}
          </dl>
          {# Only show delete link for imported layers #}
          {% if layerversion.layer_source == layer_source.TYPE_IMPORTED %}
          <i class="icon-trash text-danger"></i>
          <a href="#delete-layer-modal"  role="button" class="text-danger"
              data-toggle="modal" data-target="#delete-layer-modal">Delete layer</a>
          {% endif %}
        </div>
      </div>
    </div>
  </div> <!-- close column 12 div -->
</div> <!-- close top row div -->

{% endblock %}
