{% extends "basebuilddetailpage.html" %}
{% load projecttags %}

{% block title %}
  {% if target %}
    {{package.fullpackagespec}} - {{ target.target }} {{ build.machine }} - {{ build.project.name }} - Toaster
  {% else %}
    {{package.fullpackagespec}} - {{ build.target_set.all|dictsort:"target"|join:", " }} {{ build.machine }} - {{ build.project.name }} - Toaster
  {% endif %}
{% endblock %}
{% block extraheadcontent %}
    <!-- functions to format package 'installed_package' alias -->
    <script>
    function fmtAliasHelp(package_name, alias, hover) {
        var r = null;
        if (alias != null && alias != '') {
            r = '<span class="text-muted"> as ' + alias + '&nbsp';
            r += '<span class="glyphicon glyphicon-question-sign get-help';
            if (hover) {
                r+= ' hover-help';
            }
            else {
                r+= ' heading-help';
            }
            r += '"';
            title = '<code>' + package_name + '</code> was renamed at packaging time and was installed on your system as <code>' + alias + '</code>';
            r += ' title="' + title + '">';
            r += '</span>';
            r += '</span>';
            document.write(r);
        }
    }
    </script>
{% endblock extraheadcontent %}
{% block localbreadcrumb %}
    {% if target %}
        <li><a href="{% url "target" build.id target.id %}">{{target.target}}</a></li>
        <li>{{package.fullpackagespec}} {% if package.alias %} as {{package.alias}}{% endif %}</li>
    {% else %}
        <li><a href="{% url "packages" build.id %}"> Packages </a></li>
        <li>{{package.fullpackagespec}}</li>
    {% endif %}
{% endblock localbreadcrumb %}

{% block pagedetailinfomain %}
    <div class="row">
      <div class="col-md-12">
        <div class="page-header build-data">
            {% block mainheading %}
            <h1>{{package.fullpackagespec}}</h1>
            {% endblock %}
        </div> <!-- page-header -->
      </div> <!-- col-md-12 page-header -->
    </div> <!-- end row -->

    {% block twocolumns %}
    <div class="row">
    <div class="col-md-8 tabbable">
        {% block tabcontent %}
        {% endblock tabcontent %}
    </div> <!-- row col-md-8 -->

    <div class="col-md-4">
      <div class="well">
        <h2>Package information</h2>

        <!-- info presented as definition list -->
        <dl class="item-info">
            <dt>
                Size
                <span class="glyphicon glyphicon-question-sign get-help" title="The size of the package"></span>
            </dt>
            <dd>
                {% comment %}
                    if recipe is absent, filesize is not 0
                {% endcomment %}
                {% if package.recipe_id > 0 %}
                    {{package.size|filtered_filesizeformat}}
                    {% if target.file_size %}
                        ({{package.size|multiply:100|divide:target.file_size}}% of included package size)
                    {% endif %}

                {% endif %}
            </dd>

            <dt>
                License
                <span class="glyphicon glyphicon-question-sign get-help" title="The license under which this package is distributed"></span>
            </dt>
            <dd>{{package.license}}</dd>

            {% comment %}
            # Removed per review on 1/18/2014 until license data population
            # problemse are resolved.
            <dt>
                License files
                <i class="icon-question-sign get-help" title="Path to the license files that apply to the package"></i>
            </dt>
            <dd></dd>
            {% endcomment %}

            <dt>
                Recipe
                <span class="glyphicon glyphicon-question-sign get-help" title="The name of the recipe building this package"></span>
            </dt>
            <dd>
                {% if package.recipe_id > 0 %}
                    <a href="{% url "recipe" build.id package.recipe_id %}"> {{package.recipe.name}} </a>
                {% else %}
                    {{package.recipe.name}}
                {% endif %}
            </dd>

            <dt>
                Recipe version
                <span class="glyphicon glyphicon-question-sign get-help" title="The version of the recipe building this package"></span>
            </dt>
            <dd>{{package.recipe.version}}</dd>

            <dt>
                Layer
                <span class="glyphicon glyphicon-question-sign get-help" title="The name of the layer providing the recipe that builds this package"></span>
            </dt>
            <dd>
                {{package.recipe.layer_version.layer.name}}
                {% if package.recipe.layer_version.layer.name|format_none_and_zero != "" %}
                    {% comment %}
                    # Removed per team meeting of 1/29/2014 until
                    # decision on index search algorithm
                    <a href="https://layers.openembedded.org"  target="_blank">
                    <i class="glyphicon glyphicon-share get-info"></i>
                    </a>
                    {% endcomment %}
                {% endif %}
            </dd>
            <dt>
                Layer branch
                <span class="glyphicon glyphicon-question-sign get-help" title="The Git branch of the layer providing the recipe that builds this package"></span>
                {%if package.recipe.layer_version.layer.local_source_dir %}
		<dd>
		<span class="text-muted">Not applicable</span>
		<span class="glyphicon glyphicon-question-sign get-help" title="The source code of {{package.recipe.layer_version.layer.name}} is not in a Git repository, so there is no branch associated with it"></span>
		</dd>
		{% endif %}
            </dt>
	    {% if not package.recipe.layer_version.layer.local_source_dir %}
            <dd>{{package.recipe.layer_version.branch}}</dd>
	    {% endif %}
            <dt>
                Layer commit
                <span class="glyphicon glyphicon-question-sign get-help" title="The Git commit of the layer providing the recipe that builds this package"></span>
                {%if package.recipe.layer_version.layer.local_source_dir %}
		<dd>
		<span class="text-muted">Not applicable</span>
		<span class="glyphicon glyphicon-question-sign get-help" title="The source code of {{package.recipe.layer_version.layer.name}} is not in a Git repository, so there is no commit associated with it"></span>
		</dd>
		{% endif %}
            </dt>

	    {% if not package.recipe.layer_version.layer.local_source_dir %}
            <dd class="iscommit">{{package.recipe.layer_version.commit}}</dd>
	    {% endif %}

        </dl>
      <div> <!-- end well -->
    </div> <!-- end 4-column section -->
    {% endblock twocolumns %}
  </div> <!-- end row -->
{% endblock pagedetailinfomain %}
