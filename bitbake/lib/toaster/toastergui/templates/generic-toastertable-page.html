{% extends project_specific|yesno:"baseprojectspecificpage.html,baseprojectpage.html" %}
{% load projecttags %}
{% load humanize %}
{% load static %}

{% block projectinfomain %}

<h2>{{title}} (<span class="table-count-{{table_name}}"></span>)
  {% if project.release %}
  <i class="glyphicon glyphicon-question-sign get-help" title="This page lists {{title|lower}} compatible with the release selected for this project, which is {{project.release.description}}"></i>
  {% endif %}
</h2>

{% url table_name project.id as xhr_table_url %}
{% include "toastertable.html" %}

{% endblock %}
