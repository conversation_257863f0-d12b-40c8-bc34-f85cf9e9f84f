{% extends "basebuilddetailpage.html" %}

{% load projecttags %}

{% block title %} {{object.name}}_{{object.version}} - {{build.target_set.all|dictsort:"target"|join:", "}} {{build.machine}} - {{build.project.name}} - Toaster {% endblock %}
{% block localbreadcrumb %}
<li><a href="{% url 'recipes' build.pk %}">Recipes</a></li>
<li>{{object.name}}_{{object.version}} </li>
{% endblock %}

{% block pagedetailinfomain %}

<!-- Begin container -->

<div class="row">
  <div class="col-md-12">
    <div class="page-header build-data">
        <h1>{{object.name}}_{{object.version}}</h1>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-8 tabbable">
    <ul class="nav nav-tabs">
        <li class="{{tab_states.1}}">
            <a href="#information" data-toggle="tab">
                <span class="glyphicon glyphicon-question-sign get-help" title="Build-related
                    information about the recipe"></span>
                Recipe details
            </a>
        </li>
        <li>
             <a href="{% url "recipe_packages" build.pk object.id %}">
                <span class="glyphicon glyphicon-question-sign get-help" title="The packaged
                    output resulting from building the recipe"></span>
                Packages ({{package_count}})
            </a>
        </li>
        <li class="{{tab_states.3}}">
            <a href="#dependencies" data-toggle="tab">
                <span class="glyphicon glyphicon-question-sign get-help" title="The recipe
                    build-time dependencies (i.e. other recipes)"></span>
                Build dependencies ({{object.r_dependencies_recipe.all.count}})
            </a>
        </li>
        <li class="{{tab_states.4}}">
            <a href="#brought-in-by" data-toggle="tab">
                <span class="glyphicon glyphicon-question-sign get-help" title="The recipe
                    build-time reverse dependencies (i.e. the recipes that
                depend on this recipe)"></span>
                Reverse build dependencies ({{object.r_dependencies_depends.all.count}})
            </a>
        </li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane {{tab_states.1}}" id="information">
            <dl class="dl-horizontal">
                <dt>
                    <span class="glyphicon glyphicon-question-sign get-help" title="The name of
                        the layer providing the recipe"></span>
                    Layer
                </dt>
                <dd>{{layer.name}}</dd>

                <dt>
                    <span class="glyphicon glyphicon-question-sign get-help" title="Path to the
                        recipe .bb file"></span>
                    Recipe file
                </dt>
                <dd><code>{{object.file_path}} {% if object.pathflags %}<i>({{object.pathflags}})</i>{% endif %}</code></dd>
                <dt>
                    <span class="glyphicon glyphicon-question-sign get-help"
                        title="The Git branch of the layer providing the
                    recipe"></span>
                    Layer branch
                </dt>
                {% if layer_version.layer.local_source_dir %}
                <dd>
		  <span class="text-muted">Not applicable</span>
		  <span class="glyphicon glyphicon-question-sign get-help" title="The source
                  code of {{layer_version.layer.name}} is not in a git repository
                  so there is no branch associated with it"></span>
                </dd>
                {% else %}
                <dd>{{layer_version.branch}}</dd>
		{% endif %}
                <dt>
                    <span class="glyphicon glyphicon-question-sign get-help" title="The Git
                        commit of the layer providing the recipe"></span>
                    Layer commit
                </dt>
                {% if layer_version.layer.local_source_dir %}
		<dd>
		  <span class="text-muted">Not applicable</span>
		  <span class="glyphicon glyphicon-question-sign get-help" title="The source
                  code of {{layer_version.layer.name}} is not in a git repository
                  so there is no commit associated with it"></span>
		</dd>
                {% else %}
                <dd class="iscommit">{{layer_version.commit}}</dd>
		{% endif %}
                {% if object.provides_set.all %}
                <dt>
                    <span class="glyphicon glyphicon-question-sign get-help"
                     title="A list of aliases by which a particular recipe can be known. The additional aliases are
                           synonyms for the recipe and can be useful satisfying dependencies of other recipes during
                           the build"></span>
                    PROVIDES
                </dt>
                 <dd><code>{% for provider in object.provides_set.all %}{{ provider.name }}&nbsp;{% endfor %}</code></dd>
                {% endif %}
            </dl>

            <h2 class="details">Tasks</h2>
            {% if not tasks %}
            <div class="alert alert-info">
                <strong>{{object.name}}_{{object.version}}</strong> does not have any tasks in this build.
            </div>
            {% else %}
            <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>
                        <span class="glyphicon glyphicon-question-sign
                            get-help" title="The running sequence of each task
                            in the build"></span>
                        Order
                    </th>
                    <th>
                        <span class="glyphicon glyphicon-question-sign get-help" title="The name
                            of the task"></span>
                        Task
                    </th>
                    <th>
                        <span class="glyphicon glyphicon-question-sign get-help" title="This
                            value tells you if a task had to run (executed) in
                        order to generate the task output, or if the output was
                    provided by another task and therefore the task didn't need
                to run (not executed)"></span>
                        Executed
                    </th>
                    <th>
                        <span class="glyphicon glyphicon-question-sign get-help" title="This
                            column tells you if 'executed' tasks succeeded or
                            failed. The column also tells you why 'not executed'
                            tasks did not need to run"></span>
                        Outcome
                    </th>
                    <th>
                        <span class="glyphicon glyphicon-question-sign get-help" title="This
                            column tells you if a task tried to restore output
                            from the <code>sstate-cache</code> directory or
                            mirrors, and reports the result: Succeeded, Failed or File
                            not in cache"></span>
                        Cache attempt
                    </th>
                  </tr>
                </thead>
                <tbody>

                    {% for task in tasks %}

                    <tr {{ task|task_color }} >

                    <td>{{task.order}}</td>
                    <td>
                        <a href="{% url "task" build.pk task.pk %}">{{task.task_name}}</a>
                        {% if task.get_description %}<span class="glyphicon
                            glyphicon-question-sign get-help hover-help"
                            title="{{task.get_description}}"></span> 
                        {% endif %}
                    </td>

                    <td>{{task.get_executed_display}}</td>

                    <td>{{task.get_outcome_display}} 
                        {% if task.outcome == task.OUTCOME_FAILED %}
                            <a href="{% url 'build_artifact' build.pk "tasklogfile" task.pk %}">
                                <span class="glyphicon glyphicon-download-alt
                                    get-help" title="Download task log
                                file"></span>
                            </a>
                        {% endif %}
                        <i class="icon-question-sign get-help hover-help" title="{{task.get_outcome_help}}"></i>
                    </td>
                    <td>
                        {% ifnotequal task.sstate_result task.SSTATE_NA %}
                            {{task.get_sstate_result_display}}
                        {% endifnotequal %}
                    </td>

                    </tr>

                    {% endfor %}
                </tbody>
            </table>
            </div>
            {% endif %}
        </div>
        <div class="tab-pane {{tab_states.3}}" id="dependencies">

            {% if not object.r_dependencies_recipe.all %}
            <div class="alert alert-info">
                <strong>{{object.name}}_{{object.version}}</strong> has no build dependencies.
            </div>
            {% else %}
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>
                            Recipe
                        </th>
                        <th>
                            Version
                        </th>
                    </tr>
                </thead>
                <tbody>

                    {% for rr in object.r_dependencies_recipe.all|dictsort:"depends_on.name" %}
                    <tr>
                        <td><a href="{% url "recipe" build.pk rr.depends_on.pk %}">{{rr.depends_on.name}}</a>
                            {% if rr.via %}
                            <span class="text-muted">satisfied via <code class="text-muted">{{rr.via.name}}</code></span>
                                <span class="glyphicon glyphicon-question-sign get-help hover-help"
                                 title="This dependency is satisfied by the PROVIDES value
                                 <code>{{rr.via.name}}</code> in the <code>{{rr.depends_on.name}}</code> recipe"></span>
                            {% endif %}
                        </td>
                        <td>{{rr.depends_on.version}}</td>
                    </tr>
                    {% endfor %}

                </tbody>
            </table>
            {% endif %}

        </div>
        <div class="tab-pane {{tab_states.4}}" id="brought-in-by">

            {% if not object.r_dependencies_depends.all %}
            <div class="alert alert-info">
                <strong>{{object.name}}_{{object.version}}</strong> has no reverse build dependencies.
            </div>
            {% else %}
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>
                            Recipe
                        </th>
                        <th>
                            Version
                        </th>
                    </tr>
                </thead>
                <tbody>

                    {% for rr in object.r_dependencies_depends.all|dictsort:"recipe.name" %}
                    <tr>
                        <td><a href="{% url "recipe" build.pk rr.recipe.pk %}">{{rr.recipe.name}}</a>
                        {% if rr.via %}
                        <span class="text-muted"> satisfied via <code class="text-muted">{{rr.via.name}}</code></span>
                            <span class="glyphicon glyphicon-question-sign get-help hover-help"
                             title="This dependency is satisfied by the PROVIDES value
                             <code>{{rr.via.name}}</code> in the <code>{{rr.depends_on.name}}</code> recipe"></i>
                        {% endif %}
                        </td>
                        <td>{{rr.recipe.version}}</td>
                    </tr>
                    {% endfor %}

                </tbody>
            </table>
            {% endif %}

        </div>
    </div>
</div>

<div class="col-md-4">
  <div class="well">
    <h2>About {{object.name}}</h2>
    <dl class="item-info">
        {% if object.summary %}
            <dt>Summary</dt>
            <dd>{{object.summary}}</dd>
        {% endif %}
        {% if object.description %}
            <dt>Description</dt>
            <dd>{{object.description}}</dd>
        {% endif %}
        {% if object.homepage %}
            <dt>Homepage</dt>
            <dd><a href="{{object.homepage}}">{{object.homepage}}</a></dd>
        {% endif %}
        {% if object.bugtracker %}
            <dt>Bugtracker</dt>
            <dd><a href="{{object.bugtracker}}">{{object.bugtracker}}</a></dd>
        {% endif %}
        {% if object.section %}
            <dt>
            Section
            <span class="glyphicon glyphicon-question-sign get-help" title="The section in which recipes should be categorized"></span>
            </dt>
            <dd>{{object.section}}</dd>
        {% endif %}
        {% if object.license %}
            <dt>License</dt>
            <dd>{{object.license}}</dd>
        {% endif %}
    </dl>
  </div>
</div>

</div> <!-- end row -->

{% endblock %}
