{% load static %}

<script src="{% static 'js/newcustomimage_modal.js' %}"></script>
<script>
  $(document).ready(function (){
    try {
      newCustomImageModalInit();
    } catch (e) {
      document.write("Sorry, An error has occurred loading this page");
      console.warn(e);
    }
  });
</script>

<div class="modal fade" id="new-custom-image-modal" aria-hidden="false">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        <h3>New custom image</h3>
      </div>

      <div class="modal-body">
        <!--
          this container is visible if there are multiple image recipes which could
          be used as a basis for the new custom image; radio buttons are added to it
          via newCustomImageModalSetRecipes() as required
        -->
        <div data-role="image-selector" style="display:none;">
          <h4>Which image do you want to customise?</h4>
          <span class="help-block text-danger" id="invalid-recipe-help" style="display:none"></span>
          <div data-role="image-selector-radios"></div>
          <div class="air"></div>
        </div>

        <h4>Name your custom image</h4>

        <div class="row">
          <div class="col-md-10">
            <p class="help-block">Image names must be unique. They should not contain spaces or capital letters, and the only allowed special character is dash (-).
            </p>
            <div class="form-group">
              <input type="text" class="form-control input-lg" placeholder="Type the custom image name" required>
            </div>
            <span class="help-block text-danger" id="invalid-name-help" style="display:none"></span>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button id="create-new-custom-image-btn" class="btn btn-primary btn-large" disabled>
          <span data-role="submit-state">Create custom image</span>
          <span data-role="loading-state" style="display:none">
            <i class="fa-pulse icon-spinner"></i>&nbsp;Creating custom image...
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
