{% extends project_specific|yesno:"baseprojectspecificpage.html,baseprojectpage.html" %}

{% load projecttags %}
{% load humanize %}
{% load static %}

{% block title %} Configuration - {{project.name}} - Toaster {% endblock %}
{% block projectinfomain %}

<script src="{% static 'js/layerDepsModal.js' %}"></script>
<script src="{% static 'js/projectpage.js' %}"></script>
<script>
  $(document).ready(function (){
    var ctx = {
      testReleaseChangeUrl: "{% url 'xhr_testreleasechange' project.id %}",
    };

    try {
      projectPageInit(ctx);
    } catch (e) {
      document.write("Sorry, An error has occurred loading this page (project):"+e);
      console.warn(e);
    }
  });
</script>

<div id="delete-project-modal" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4>Are you sure you want to delete this project?</h4>
      </div>
      <div class="modal-body">
        <p>Deleting the <strong class="project-name"></strong> project
        will:</p>
        <ul>
          <li>Cancel its builds currently in progress</li>
          <li>Remove its configuration information</li>
          <li>Remove its imported layers</li>
          <li>Remove its custom images</li>
          <li>Remove all its build information</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="delete-project-confirmed">
          <span data-role="submit-state">Delete project</span>
          <span data-role="loading-state" style="display:none">
            <span class="fa-pulse">
            <i class="fa-pulse icon-spinner"></i>
          </span>
            &nbsp;Deleting project...
          </span>
        </button>
        <button type="button" class="btn btn-link" data-dismiss="modal">Cancel</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>


<div class="row" id="project-page" style="display:none">
  <div class="col-md-6">
    <div class="well well-transparent" id="machine-section">
      <h3>Machine</h3>

      <p class="lead"><span id="project-machine-name"></span> <span class="glyphicon glyphicon-edit" id="change-machine-toggle"></span></p>

      <form id="select-machine-form" style="display:none;" class="form-inline">
        <span class="help-block">Machine suggestions come from the list of layers added to your project. If you don't see the machine you are looking for, <a href="{% url 'projectmachines' project.id %}">check the full list of machines</a></span>
        <div class="form-group" id="machine-input-form">
          <input class="form-control" id="machine-change-input" autocomplete="off" value="" data-provide="typeahead" data-minlength="1" data-autocomplete="off" type="text">
        </div>
        <button id="machine-change-btn" class="btn btn-default" type="button">Save</button>
        <a href="#" id="cancel-machine-change" class="btn btn-link">Cancel</a>
        <span class="help-block text-danger" id="invalid-machine-name-help" style="display:none">A valid machine name cannot include spaces.</span>
        <p class="form-link"><a href="{% url 'projectmachines' project.id %}">View compatible machines</a></p>
      </form>
    </div>

    <div class="well well-transparent" id="distro-section">
      <h3>Distro</h3>

      <p class="lead"><span id="project-distro-name"></span> <span class="glyphicon glyphicon-edit" id="change-distro-toggle"></span></p>

      <form id="select-distro-form" style="display:none;" class="form-inline">
        <span class="help-block">Distro suggestions come from the Layer Index</a></span>
        <div class="form-group">
          <input class="form-control" id="distro-change-input" autocomplete="off" value="" data-provide="typeahead" data-minlength="1" data-autocomplete="off" type="text">
        </div>
        <button id="distro-change-btn" class="btn btn-default" type="button">Save</button>
        <a href="#" id="cancel-distro-change" class="btn btn-link">Cancel</a>
        <p class="form-link"><a href="{% url 'projectdistros' project.id %}">View compatible distros</a></p>
      </form>
    </div>

	{% if not project_specific %}
    <div class="well well-transparent">
      <h3>Most built recipes</h3>

      <div class="alert alert-info" style="display:none" id="no-most-built">
        <h4>You haven't built any recipes yet</h4>
        <p class="form-link"><a href="{% url 'projectimagerecipes' project.id %}">Choose a recipe to build</a></p>
      </div>

      <ul class="list-unstyled lead" id="freq-build-list">
      </ul>
      <button class="btn btn-primary" id="freq-build-btn" disabled="disabled">Build selected recipes</button>
    </div>
    {% endif %}

    <div class="well well-transparent">
      <h3>Project release</h3>

      <p class="lead"><span id="project-release-title"></span>

      <!-- Comment out the ability to change the project release, until we decide what to do with this functionality -->

      <!--i title="" data-original-title="" id="release-change-toggle" class="icon-pencil"></i-->
      </p>

      <!-- Comment out the ability to change the project release, until we decide what to do with this functionality -->

      <!--form class="form-inline" id="change-release-form" style="display:none;">
        <select></select>
        <button class="btn" style="margin-left:5px;" id="change-release-btn">Change</button> <a href="#" id="cancel-release-change" class="btn btn-link">Cancel</a>
      </form-->
    </div>
  </div>

  <div class="col-md-6">
    <div class="well well-transparent" id="layer-container">
      <h3>Layers <span class="counter">(<span id="project-layers-count"></span>)</span>
        <span title="OpenEmbedded organises recipes and machines into thematic groups called <strong>layers</strong>. Click on a layer name to see the recipes and machines it includes." class="glyphicon glyphicon-question-sign get-help"></span>
      </h3>

      <div class="alert alert-warning" id="no-layers-in-project" style="display:none">
        <h4>This project has no layers</h4>
        In order to build this project you need to add some layers first. For that you can:
        <ul>
          <li><a href="{% url 'projectlayers' project.id %}">Choose from the layers compatible with this project</a></li>
          <li><a href="{% url 'importlayer' project.id %}">Import a layer</a></li>
          <li><a href="http://docs.yoctoproject.org/dev-manual/common-tasks.html#understanding-and-creating-layers" target="_blank">Read about layers in the documentation</a></li>
          <li>Or type a layer name below</li>
        </ul>
      </div>

      <form class="form-inline">
        <div class="form-group">
          <input id="layer-add-input" class="form-control" autocomplete="off" placeholder="Type a layer name" data-minlength="1" data-autocomplete="off" data-provide="typeahead" data-source="" type="text">
        </div>
        <button id="add-layer-btn" class="btn btn-default" disabled>Add layer</button>
        <p class="form-link">
          <a href="{% url 'projectlayers' project.id %}" id="view-compatible-layers">View compatible layers</a>
          <span class="text-muted">|</span>
          <a href="{% url 'importlayer' project.id %}">Import layer</a>
        </p>
      </form>

      <ul class="list-unstyled lead" id="layers-in-project-list">
      </ul>
  </div>

</div>
{% endblock %}
