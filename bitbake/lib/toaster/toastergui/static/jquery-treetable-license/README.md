# jQuery treetable

jQuery treetable is a plugin for jQuery, the 'Write Less, Do More, JavaScript
Library'. With this plugin you can display a tree in an HTML table, e.g. a
directory structure or a nested list. Why not use a list, you say? Because lists
are great for displaying a tree, and tables are not. Oh wait, but this plugin
uses tables, doesn't it? Yes. Why do I use a table to display a list? Because I
need multiple columns to display additional data besides the tree.

Download the latest release from the jQuery Plugin Registry or grab the source
code from Github. Please report issues through Github issues. This plugin is
released under both the MIT and the GPLv2 license by <PERSON><PERSON>.

## Documentation and Examples

See index.html for technical documentation and examples. The most recent version
of this document is also available online at
http://ludo.cubicphuse.nl/jquery-treetable. An AJAX enabled example built with
Ruby on Rails can be found at
https://github.com/ludo/jquery-treetable-ajax-example.
