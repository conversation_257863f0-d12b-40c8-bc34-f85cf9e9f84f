/* New Toaster custom css file for Bootstrap 3 */

/* Set required top body padding for the fixed top navbard */
body { padding-top: 50px; }

/* Style the Yocto Project logo */
img.logo { height: 30px; vertical-align: bottom; }

/* Style the Yocto Project logo and the Toaster name in the top navbar */
.toaster-navbar-brand { float: left; margin: 7px 25px 0 0; }
.toaster-navbar-brand a.brand { color: #777; height: 50px; padding: 15px 5px 15px 15px; font-size: 20px; line-height: 25px; display: inline; }
.toaster-navbar-brand > a { text-decoration: none; }
.toaster-navbar-brand > a.brand:hover { color: #5e5e5e; }

/* Style the debugging information in the top navbar */
.glyphicon-info-sign { color: #777; font-size: 16px; }
.glyphicon-info-sign:hover { color: #999; cursor: pointer; }

/* Override the negative right margin for the navbar-right class */
#new-project-button { margin-right: 0; }

/* Increase popovers width to fit commit SHAs */
.popover { max-width: 350px; }

/* Set a limit to popover height to handle long dependency lists */
.popover-content { max-height: 350px; overflow: scroll; }

/* Set a limit to modal dialogs height to handle long variable history */
[id^="variable-"] .modal-content { max-height: 550px; overflow-y: scroll; }

/* Make sure long values in variable history do not make the modal dialogs
 * scroll horizontally */
[id^="variable-"] .modal-content p { word-break: break-all; }

/* Increase bottom margin of definition lists inside popovers for the Toaster version information in the top navbar, and also inside the right hand columns of our details pages */
.popover-content dd,
.item-info dd { margin-bottom: 15px; }

/* Style the horizontal definition lists */
.dl-horizontal dt { width: 200px; line-height: 25px; }
.dl-horizontal dd { margin-left: 220px; line-height: 25px; }

/* Style our build results */
.build-result .progress { margin-bottom: 0; }
.alert-link.build-warnings,
.glyphicon-warning-sign.build-warnings { color: #8a6d3b; }
.build-result .project-name { margin-top: -10px; margin-bottom: 5px; }
.rebuild-btn, .cancel-build-btn { cursor: pointer; }

/* Styles for the help information */
.get-help { color: #CCCCCC; }
.get-help:hover { color: #999999; cursor: pointer; }
.get-help-green { color: #3c763d; }
.get-help-green:hover { color: #2b542c; cursor: pointer; }
.get-help-blue { color: #3A87AD; }
.get-help-blue:hover { color: #005580; cursor: pointer; }
.get-help-red { color: #a94442; }
.get-help-red:hover { color: #843534; cursor: pointer; }

/* Styles for our table controls */
.form-control[id^="search-input-"],
.form-control[id^="new-search-input-"],
#search{ width: 30em; }
#search-input-selectpackagestable,
#search-input-packagestable,
.form-control[id^="no-results-search-input-"] { width: 20em; }
#edit-columns-button { margin-right: 30px; }
.navbar-default[id^="table-chrome-"],
#variables .navbar-default { background-color: transparent; }
[id^="table-chrome-collapse-"] .navbar-form { margin-left: -15px; }
.dropdown-menu.editcol { padding-left: 10px; min-width: 200px; }
span[class^="remove-search-btn-"] { position: absolute; right: 5px; top: 0; bottom: 0; height: 14px; margin: auto; font-size: 14px; cursor: pointer; color: #777;}
span[class^="remove-search-btn-"]:hover { color: #333; }
#no-results-special-selectpackagestable .form-inline { margin-top: 20px; }
[id^="pagination-"] .pagination,
[id^="pagination-"] .navbar-form { margin-top: 0; }
[id^="table-chrome-"] .navbar-form { margin-left: -15px; margin-right: -15px; }
[id^="table-chrome-"] .detail-page-contols,
#packages-built .detail-page-controls { padding-left: 0; padding-right: 0; }

/* Override the default font-weight for labels: it's a bit too much */
label { font-weight: normal; }

/* Firefox workaround for awkward fieldset styling. See http://getbootstrap.com/css/#tables-responsive */
@-moz-document url-prefix() { fieldset { display: table-cell; } }

/* Table heading sortable / not sortable states */
thead > tr > th > a { font-weight: normal; }
thead > tr > th > a.sorted { font-weight: bold; color: #333; }

/* Give some extra space to the 'clear filter' buttons */
.tooltip .btn { margin: 5px; }

/* In table headings, separate the help bubble from the column heading */
thead > tr > th > .glyphicon-question-sign { margin-right: 5px; }

/* Style build outcome in tables, download, remove and change icons */
tbody > tr > td > .glyphicon-ok-circle,
dd > .glyphicon-ok-circle { color: #3c763d; }
tbody > tr > td > .glyphicon-minus-sign { color: #a94442; }
.glyphicon-download-alt,
.glyphicon-edit { color: #337ab7; }
.failed_tasks .glyphicon-download-alt { margin-left: 5px; }
.glyphicon-download-alt:hover,
.glyphicon-edit:hover { color: #23527c; cursor: pointer; text-decoration: none; }
.glyphicon-trash { color: #a94442; }
.btn-danger > .glyphicon-trash,
.btn-danger > .glyphicon-trash:hover { color: #fff; }
.glyphicon-trash:hover { color: #843534; cursor: pointer; }

/* Set the font size for icons inside headings, lead paragraphs and definition lists */
h1 > .glyphicon-edit,
p.lead .glyphicon { font-size: 16px; }
h2 > .glyphicon-question-sign,
h3 > .glyphicon-question-sign,
.heading-help { font-size: 14px; }

/* Create a class for wells without background colour */
.well-transparent { background-color: transparent; }

/* Create a class for the left navigation headers */
.nav-header { display: block; font-size: 12px; font-weight: bold; line-height: 20px; color: #999; text-transform: uppercase; margin-top: 20px !important; margin-bottom: 15px; padding-left: 15px; }

/* Increase the tabs padding and margin in the project pages to fit the build form and the main content */
#project-topbar .nav-tabs > li > a { padding: 15px; }
#project-topbar { margin-bottom: 20px; }

/* Style the project name change form in the project pages */
#project-name-change-input { width: 20em; }

/* Style the build form in the project pages */
#project-topbar .input-lg { width: 18em; }
#project-topbar form .glyphicon { top: 15px; right: 4px; }
#build-button { padding: 10px 30px; }

/* Style the form links in the project page (all machines, all layers, etc) */
.form-link { margin-top: 10px; }

/* Style the most built recipes list in the project page */
#freq-build-list .checkbox input[type="checkbox"] { position: relative; margin: 0 10px 0 0; vertical-align: middle; }
#freq-build-list.lead > li { line-height: 25px; }
#freq-build-list { margin-top: 20px; }
#freq-build-list label { padding-left: 0; }
#freq-build-btn { margin-top: 10px; }
#no-most-built { margin-top: 20px; }

/* Style the layers section in the project page and the layer dependencies in the import layer form */
#layer-container .form-inline { margin-top: 20px; }
#layer-add-input { width: 17em; }
ul.lead { margin-top: 20px; }
ul.lead > li { line-height: 38px; }
ul.lead .glyphicon-trash,
ul.lead .glyphicon-trash { font-size: 16px; margin-left: 7px; }
#layers-in-project-list .tooltip-inner  { max-width: 600px; }
#no-layers-in-project { margin-top: 20px; }
#no-layers-in-project ul { margin-top: 10px; }

/* Style the layer information icons in the layer details pages */
dd .glyphicon-trash,
dd .glyphicon-edit { margin-left: 5px; }

/* Style the forms and definition lists in the layer details pages */
#change-repo-form .form-control { width: 17em; }
#information { margin-bottom: 5em; }
#information dd > form { margin-bottom: 5px; margin-top: 5px; }
#edit-layer-source-form fieldset { margin-top: 20px; }
#directory-info,
#git-repo-info { margin-top: 20px; }
#layer-dir-path-in-details { width: 55%; }
.add-deps .form-control { width: 15em; }

/* Style the forms and definition lists in the BitBake variables page */
.variable-list { margin-bottom: 20px; }
dd.variable-list form { margin-top: 10px; }
#new-dl_dir,
#filter-image_fstypes,
#new-image_install,
#new-sstate_dir,
#new-imagefs_types { width: 20em; }
#package_classes-select { width: 10em; }
.scrolling { border: 1px solid #dddddd; height: 154px; overflow: auto; padding: 0 10px; width: 27.5%; margin-bottom: 10px; margin-top: 10px; }
.scrolling.has-error { border-color: #a94442; }
.help-block.text-danger { color: #a94442; }
.tooltip-inner code { color: #fff; }
.text-danger > code { color: #a94442; }
dd.variable-list .glyphicon-question-sign { font-size: 14px; }
dd.variable-list .glyphicon-edit { font-size: 16px; }
dt .glyphicon-trash { margin-left: 5px; font-size: 16px; }
#change-package_classes-form .checkbox { margin-top: 5px; }
#variable-form h5 { margin-top: 0; }
#variable-form .col-md-5 { padding-left: 45px; }

/* Create a class for additional top margin that we can use in headings */
.top-air { margin-top: 40px; }

/* Add some bottom margin to our h2's */
h2 { margin-bottom: 25px; }

/* Style the typeahead */
.tt-menu { min-width: 400px; padding-bottom: 10px; }
.tt-suggestion { padding: 5px 10px; }
.tt-suggestion:hover,
.tt-suggestion:active { background-color: #f5f5f5; cursor: pointer; }

/* Style the import layer form controls*/
legend { border: none; }
fieldset.fields-apart-from-layer-name { margin-top: 20px; }
.radioLegend { margin-bottom: 0; }
#layer-name-ctrl { margin-top: 20px; }
#import-layer-name,
#layer-subdir { width: 20%; }
#layer-git-repo-url { width: 40%; }
#local-dir-path { width: 45%; }
#layer-dependency { width: 16em; }
#layer-deps-list { margin-top: 0; }
#form-actions { margin-bottom: 30px; }
#duplicate-layer-info dl { margin-top: 10px; }
#duplicate-layer-info dd { margin-bottom: 10px; }
.help-inline { color: #737373; margin-left: 10px; }
.radio-help { width: 50%; margin-left: 20px; }
#repo-select div:nth-of-type(2) { margin-top: 15px; }

/* Give some padding to the in-cell tooltips we use for notifications in tables */
td > .tooltip-inner,
.inline-notification { padding: 10px; }

/* Set sane widths for table columns */
#newcustomimagestable .get_description_or_summary,
#imagerecipestable .get_description_or_summary,
#softwarerecipestable .get_description_or_summary,
#layerstable .layer__summary { width: 30%; }
#recipestable .get_description_or_summary { width: 40%; }
#machinestable .name { white-space: nowrap; }
#machinestable .description { width: 45%; }
#otable .variable_value,
#otable .file { word-break: break-all; width: 25%; }
[id^="variable-"] .file { word-break: break-all; }

/* For the tables still not ported to ToasterTables, style the table headings
 * that are not sortable */
th > span.text-muted { font-weight: normal; }

/* Override the rather ugly default code styles */
code { color: #333; background-color: transparent; }

/* Style our breadcrumbs */
.breadcrumb > li + li::before { content: none; }
.breadcrumb { background-color: transparent; padding-left: 0; padding-top: 15px; }
.breadcrumb .divider { color: #777; margin: 0 5px; }

/* Reduce top margin for the page-header class */
.page-header { margin-top: 30px; }

/* Set some space around the layer button in the layer details pages */
.tab-content { margin-top: 20px; }
.tab-pane { margin-top: 20px; }

/* Style the new window icons */
.glyphicon-new-window:hover { text-decoration: none; }
.dl-horizontal > dd > .glyphicon-new-window { margin-left: 5px; }

/* Style the special no results message in the custom image details page */
[id^="no-results-special-"] > .alert-warning > ol { margin-top: 10px; }

/* style the loading spinner in the new custom image dialog */
#create-new-custom-image-btn [data-role="loading-state"] {
  padding-left: 16px;
}

/* icon has to be absolutely positioned, otherwise the spin animation doesn't work */
#create-new-custom-image-btn [data-role="loading-state"] .icon-spinner {
  position: absolute;
  left: 26px;
  bottom: 26px;
}

/* Style the content of modal dialogs */
.modal-footer { text-align: left; }
.date-filter-controls { margin-top: 10px; }
.date-filter-controls span { margin: 0 10px; }

/* Style the fixed positioned notifications */
#loading-notification { position: fixed; z-index: 1101; top: 3%; left: 40%; right: 40%; -webkit-box-shadow: 0 0 10px #c09853; -moz-box-shadow: 0 0 10px #c09853; box-shadow: 0 0 10px #c09853; }

.change-notification { position: fixed; z-index: 1101; top: 4%; left: 30%; right: 30%; -webkit-box-shadow: 0 0 10px #3a87ad; -moz-box-shadow: 0 0 10px #3a87ad; box-shadow: 0 0 10px #3a87ad; }

.alert-success.change-notification { -webkit-box-shadow: 0 0 10px #3c763d; -moz-box-shadow: 0 0 10px #3c763d; box-shadow: 0 0 10px #3c763d; }

/* Style the new project form */
#new-project-name { width: 33%; }
#projectversion { width: 20%; margin-bottom: 10px; }

/* Style the Toaster screenshot in the landing page */
.img-thumbnail { padding: 0; }

/* Set the layout for the build information pages */

#nav { margin-top: 10px; }
.page-header.build-data { margin-top: 0px; }
.build-data > h1 { margin-top: 8px; }

/* Style the build outcome information in the build dashboard */
.log { margin-left: 30px; }
.show-warnings { font-weight: 700; color: #8a6d3b; }
.show-warnings:hover { color: #66512c; }

/* Style the errors and warnings information in the build dashboard */
#errors .panel-heading { background-color: transparent; color: #843534; }
#warnings .panel-heading { background-color: transparent; color: #8a6d3b; }
#warnings .panel-heading a:hover { color: #66512c; }
h2.panel-title { font-size: 30px; }
.alert-danger pre,
.alert-warning pre { background-color: transparent; border: none; }
.alert-danger pre { color: #a94442; }
#error-info pre,
#warning-info pre { white-space: pre-wrap; }
.alert-warning pre { color: #8a6d3b; }

/* Style the wells in the build dashboard */
.dashboard-section h3 { margin-top: 10px; margin-bottom: 20px; }
.col-md-4.dashboard-section dd { margin-bottom: 10px; }

/* Make the help in tables insivisble until you hover over the right cell */
.hover-help { visibility: hidden; }

#add-remove-layer-btn { margin-bottom: 20px; }

/* Blue hightlight animation for tasks and directory structure tables */
.highlight { -webkit-animation: target-fade 15s 1; -moz-animation: target-fade 15s 1; animation: target-fade 15s 1; }
@-webkit-keyframes target-fade { 0% { background-color: #D9EDF7; } 25% { background-color: #D9EDF7; } 100% { background-color: white; } }
@-moz-keyframes target-fade { 0% { background-color: #D9EDF7; } 25% { background-color: #D9EDF7; } 100% { background-color: white; } }
@keyframes target-fade { 0% { background-color: #D9EDF7; } 25% { background-color: #D9EDF7; } 100% { background-color: white; } }

/* Copied in from newer version of Font-Awesome 4.3.0 */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
  display: inline-block;
}
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
  display: inline-block;
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* End copied in from newer version of Font-Awesome 4.3.0 */
