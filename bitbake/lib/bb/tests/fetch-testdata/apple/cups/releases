





<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
  <link rel="dns-prefetch" href="https://github.githubassets.com">
  <link rel="dns-prefetch" href="https://avatars0.githubusercontent.com">
  <link rel="dns-prefetch" href="https://avatars1.githubusercontent.com">
  <link rel="dns-prefetch" href="https://avatars2.githubusercontent.com">
  <link rel="dns-prefetch" href="https://avatars3.githubusercontent.com">
  <link rel="dns-prefetch" href="https://github-cloud.s3.amazonaws.com">
  <link rel="dns-prefetch" href="https://user-images.githubusercontent.com/">



  <link crossorigin="anonymous" media="all" integrity="sha512-7mtunHqp/Bw0ND9akjJME8XCh0WPm3HAXOSeX7skL0qGAhpdfzkQvYcujYcwNPTpWKeKMFUGZGtvnEkcczFgwQ==" rel="stylesheet" href="https://github.githubassets.com/assets/frameworks-9b5314213e37056ed87b0418056c4f2c.css" />
  <link crossorigin="anonymous" media="all" integrity="sha512-CmoegizWCUR1jC94Y2eukVQIFxJ9GxYerz9q7dBwImLlx8ODwYkXAMIhCfTnA45Ep6++rcO/ZtKVLvFBM8dapA==" rel="stylesheet" href="https://github.githubassets.com/assets/site-4e9f27fa33341743f730ae7b0e33eff5.css" />
    <link crossorigin="anonymous" media="all" integrity="sha512-BvnICKFjIvT69o61dyYllXtaOnGVb7Ifj5c3lk3wj7tkjat2ICuN+XRwyk8tqP3dj7IFhEQzxDdxSHaJ3xj3Mw==" rel="stylesheet" href="https://github.githubassets.com/assets/github-ff986874cf7e28cbcd5d448cdca7245d.css" />
    
    
    
    


  <meta name="viewport" content="width=device-width">
  
  <title>Releases · apple/cups · GitHub</title>
    <meta name="description" content="Official CUPS Sources. Contribute to apple/cups development by creating an account on GitHub.">
    <link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="GitHub">
  <link rel="fluid-icon" href="https://github.com/fluidicon.png" title="GitHub">
  <meta property="fb:app_id" content="****************">

    <meta name="twitter:image:src" content="https://avatars2.githubusercontent.com/u/********?s=400&amp;v=4" /><meta name="twitter:site" content="@github" /><meta name="twitter:card" content="summary" /><meta name="twitter:title" content="apple/cups" /><meta name="twitter:description" content="Official CUPS Sources. Contribute to apple/cups development by creating an account on GitHub." />
    <meta property="og:image" content="https://avatars2.githubusercontent.com/u/********?s=400&amp;v=4" /><meta property="og:site_name" content="GitHub" /><meta property="og:type" content="object" /><meta property="og:title" content="apple/cups" /><meta property="og:url" content="https://github.com/apple/cups" /><meta property="og:description" content="Official CUPS Sources. Contribute to apple/cups development by creating an account on GitHub." />

  <link rel="assets" href="https://github.githubassets.com/">
  
  

    <meta name="request-id" content="E0CB:22348:508D9B:74D7B0:5DFA43C4" data-pjax-transient>



  

  <meta name="selected-link" value="repo_releases" data-pjax-transient>

      <meta name="google-site-verification" content="KT5gs8h0wvaagLKAVWq8bbeNwnZZK1r1XQysX3xurLU">
    <meta name="google-site-verification" content="ZzhVyEFwb7w3e0-uOTltm8Jsck2F5StVihD0exw2fsA">
    <meta name="google-site-verification" content="GXs5KoUUkNCoaAZn7wPN-t01Pywp9M3sEjnt_3_ZWPc">

    <meta name="octolytics-host" content="collector.githubapp.com" /><meta name="octolytics-app-id" content="github" /><meta name="octolytics-event-url" content="https://collector.githubapp.com/github-external/browser_event" /><meta name="octolytics-dimension-request_id" content="E0CB:22348:508D9B:74D7B0:5DFA43C4" /><meta name="octolytics-dimension-region_edge" content="ams" /><meta name="octolytics-dimension-region_render" content="iad" /><meta name="octolytics-dimension-ga_id" content="" class="js-octo-ga-id" /><meta name="octolytics-dimension-visitor_id" content="3868440108365136837" />

<meta name="analytics-location" content="/&lt;user-name&gt;/&lt;repo-name&gt;/releases/index" data-pjax-transient="true" />



    <meta name="google-analytics" content="UA-3769691-2">


<meta class="js-ga-set" name="dimension1" content="Logged Out">



  

      <meta name="hostname" content="github.com">
    <meta name="user-login" content="">

      <meta name="expected-hostname" content="github.com">

      <meta name="js-proxy-site-detection-payload" content="****************************************************************************************************************************************************************************************************************************************************************">

    <meta name="enabled-features" content="MARKETPLACE_FEATURED_BLOG_POSTS,MARKETPLACE_INVOICED_BILLING,MARKETPLACE_SOCIAL_PROOF_CUSTOMERS,MARKETPLACE_TRENDING_SOCIAL_PROOF,MARKETPLACE_RECOMMENDATIONS,MARKETPLACE_PENDING_INSTALLATIONS">

    <meta name="html-safe-nonce" content="99799050425011fe6bc71791e860bd50b46cc6a7">

  <meta http-equiv="x-pjax-version" content="2d29eb011c4e56b5682393d6f45c86b9">
  

      <link rel="alternate" type="application/atom+xml" title="cups Release Notes" href="https://github.com/apple/cups/releases.atom" />
  <link rel="alternate" type="application/atom+xml" title="cups Tags" href="https://github.com/apple/cups/tags.atom" />
  <link href="https://github.com/apple/cups/commits/master.atom" rel="alternate" title="Recent Commits to cups:master" type="application/atom+xml">

  <meta name="go-import" content="github.com/apple/cups git https://github.com/apple/cups.git">

  <meta name="octolytics-dimension-user_id" content="********" /><meta name="octolytics-dimension-user_login" content="apple" /><meta name="octolytics-dimension-repository_id" content="44137852" /><meta name="octolytics-dimension-repository_nwo" content="apple/cups" /><meta name="octolytics-dimension-repository_public" content="true" /><meta name="octolytics-dimension-repository_is_fork" content="false" /><meta name="octolytics-dimension-repository_network_root_id" content="44137852" /><meta name="octolytics-dimension-repository_network_root_nwo" content="apple/cups" /><meta name="octolytics-dimension-repository_explore_github_marketplace_ci_cta_shown" content="false" />




  <meta name="browser-stats-url" content="https://api.github.com/_private/browser/stats">

  <meta name="browser-errors-url" content="https://api.github.com/_private/browser/errors">

  <link rel="mask-icon" href="https://github.githubassets.com/pinned-octocat.svg" color="#000000">
  <link rel="icon" type="image/x-icon" class="js-site-favicon" href="https://github.githubassets.com/favicon.ico">

<meta name="theme-color" content="#1e2327">





  <link rel="manifest" href="/manifest.json" crossOrigin="use-credentials">

  </head>

  <body class="logged-out env-production page-responsive">
    

  <div class="position-relative js-header-wrapper ">
    <a href="#start-of-content" tabindex="1" class="px-2 py-4 bg-blue text-white show-on-focus js-skip-to-content">Skip to content</a>
    <span class="Progress progress-pjax-loader position-fixed width-full js-pjax-loader-bar">
      <span class="progress-pjax-loader-bar top-0 left-0" style="width: 0%;"></span>
    </span>

    
    
    


        <header class="Header-old header-logged-out js-details-container Details position-relative f4 py-2" role="banner">
  <div class="container-lg d-lg-flex flex-items-center p-responsive">
    <div class="d-flex flex-justify-between flex-items-center">
        <a class="mr-4" href="https://github.com/" aria-label="Homepage" data-ga-click="(Logged out) Header, go to homepage, icon:logo-wordmark">
          <svg height="32" class="octicon octicon-mark-github text-white" viewBox="0 0 16 16" version="1.1" width="32" aria-hidden="true"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"/></svg>
        </a>

          <div class="d-lg-none css-truncate css-truncate-target width-fit p-2">
            
              <svg class="octicon octicon-repo" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M4 9H3V8h1v1zm0-3H3v1h1V6zm0-2H3v1h1V4zm0-2H3v1h1V2zm8-1v12c0 .55-.45 1-1 1H6v2l-1.5-1.5L3 16v-2H1c-.55 0-1-.45-1-1V1c0-.55.45-1 1-1h10c.55 0 1 .45 1 1zm-1 10H1v2h2v-1h3v1h5v-2zm0-10H2v9h9V1z"/></svg>
    <a class="Header-link" href="/apple">apple</a>
    /
    <a class="Header-link" href="/apple/cups">cups</a>


          </div>

        <div class="d-flex flex-items-center">
            <a href="/join?source=header-repo"
              class="d-inline-block d-lg-none f5 text-white no-underline border border-gray-dark rounded-2 px-2 py-1 mr-3 mr-sm-5"
              data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="2463445c045c6be86a7b48e6c0af96d0ba777892d6d236f27826323b35cef5d7"
              data-ga-click="(Logged out) Header, clicked Sign up, text:sign-up">
              Sign&nbsp;up
            </a>

          <button class="btn-link d-lg-none mt-1 js-details-target" type="button" aria-label="Toggle navigation" aria-expanded="false">
            <svg height="24" class="octicon octicon-three-bars text-white" viewBox="0 0 12 16" version="1.1" width="18" aria-hidden="true"><path fill-rule="evenodd" d="M11.41 9H.59C0 9 0 8.59 0 8c0-.59 0-1 .59-1H11.4c.59 0 .59.41.59 1 0 .59 0 1-.59 1h.01zm0-4H.59C0 5 0 4.59 0 4c0-.59 0-1 .59-1H11.4c.59 0 .59.41.59 1 0 .59 0 1-.59 1h.01zM.59 11H11.4c.59 0 .59.41.59 1 0 .59 0 1-.59 1H.59C0 13 0 12.59 0 12c0-.59 0-1 .59-1z"/></svg>
          </button>
        </div>
    </div>

    <div class="HeaderMenu HeaderMenu--logged-out position-fixed top-0 right-0 bottom-0 height-fit position-lg-relative d-lg-flex flex-justify-between flex-items-center flex-auto">
      <div class="d-flex d-lg-none flex-justify-end border-bottom bg-gray-light p-3">
        <button class="btn-link js-details-target" type="button" aria-label="Toggle navigation" aria-expanded="false">
          <svg height="24" class="octicon octicon-x text-gray" viewBox="0 0 12 16" version="1.1" width="18" aria-hidden="true"><path fill-rule="evenodd" d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48L7.48 8z"/></svg>
        </button>
      </div>

        <nav class="mt-0 px-3 px-lg-0 mb-5 mb-lg-0" aria-label="Global">
          <ul class="d-lg-flex list-style-none">
              <li class="d-block d-lg-flex flex-lg-nowrap flex-lg-items-center border-bottom border-lg-bottom-0 mr-0 mr-lg-3 edge-item-fix position-relative flex-wrap flex-justify-between d-flex flex-items-center ">
                <details class="HeaderMenu-details details-overlay details-reset width-full">
                  <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
                    Why GitHub?
                    <svg x="0px" y="0px" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative">
                      <path d="M1,1l6.2,6L13,1"></path>
                    </svg>
                  </summary>
                  <div class="dropdown-menu flex-auto rounded-1 bg-white px-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
                    <a href="/features" class="py-2 lh-condensed-ultra d-block link-gray-dark no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Features">Features <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a>
                    <ul class="list-style-none f5 pb-3">
                      <li class="edge-item-fix"><a href="/features/code-review/" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Code review">Code review</a></li>
                      <li class="edge-item-fix"><a href="/features/project-management/" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Project management">Project management</a></li>
                      <li class="edge-item-fix"><a href="/features/integrations" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Integrations">Integrations</a></li>
                      <li class="edge-item-fix"><a href="/features/actions" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Actions">Actions</a></li>
                          <li class="edge-item-fix"><a href="/features/packages" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to GitHub Packages">Packages</a></li>
                      <li class="edge-item-fix"><a href="/features/security" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Security">Security</a></li>
                      <li class="edge-item-fix"><a href="/features#team-management" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Team management">Team management</a></li>
                      <li class="edge-item-fix"><a href="/features#hosting" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Code hosting">Hosting</a></li>
                    </ul>

                    <ul class="list-style-none mb-0 border-lg-top pt-lg-3">
                      <li class="edge-item-fix"><a href="/customer-stories" class="py-2 lh-condensed-ultra d-block no-underline link-gray-dark no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Customer stories">Customer stories <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="/security" class="py-2 lh-condensed-ultra d-block no-underline link-gray-dark no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Security">Security <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a></li>
                    </ul>
                  </div>
                </details>
              </li>
              <li class="border-bottom border-lg-bottom-0 mr-0 mr-lg-3">
                <a href="/enterprise" class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-ga-click="(Logged out) Header, go to Enterprise">Enterprise</a>
              </li>

              <li class="d-block d-lg-flex flex-lg-nowrap flex-lg-items-center border-bottom border-lg-bottom-0 mr-0 mr-lg-3 edge-item-fix position-relative flex-wrap flex-justify-between d-flex flex-items-center ">
                <details class="HeaderMenu-details details-overlay details-reset width-full">
                  <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
                    Explore
                    <svg x="0px" y="0px" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative">
                      <path d="M1,1l6.2,6L13,1"></path>
                    </svg>
                  </summary>

                  <div class="dropdown-menu flex-auto rounded-1 bg-white px-0 pt-2 pb-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
                    <ul class="list-style-none mb-3">
                      <li class="edge-item-fix"><a href="/explore" class="py-2 lh-condensed-ultra d-block link-gray-dark no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Explore">Explore GitHub <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a></li>
                    </ul>

                    <h4 class="text-gray-light text-normal text-mono f5 mb-2 border-lg-top pt-lg-3">Learn &amp; contribute</h4>
                    <ul class="list-style-none mb-3">
                      <li class="edge-item-fix"><a href="/topics" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Topics">Topics</a></li>
                        <li class="edge-item-fix"><a href="/collections" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Collections">Collections</a></li>
                      <li class="edge-item-fix"><a href="/trending" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Trending">Trending</a></li>
                      <li class="edge-item-fix"><a href="https://lab.github.com/" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Learning lab">Learning Lab</a></li>
                      <li class="edge-item-fix"><a href="https://opensource.guide" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Open source guides">Open source guides</a></li>
                    </ul>

                    <h4 class="text-gray-light text-normal text-mono f5 mb-2 border-lg-top pt-lg-3">Connect with others</h4>
                    <ul class="list-style-none mb-0">
                      <li class="edge-item-fix"><a href="https://github.com/events" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Events">Events</a></li>
                      <li class="edge-item-fix"><a href="https://github.community" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Community forum">Community forum</a></li>
                      <li class="edge-item-fix"><a href="https://education.github.com" class="py-2 pb-0 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to GitHub Education">GitHub Education</a></li>
                    </ul>
                  </div>
                </details>
              </li>

              <li class="border-bottom border-lg-bottom-0 mr-0 mr-lg-3">
                <a href="/marketplace" class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-ga-click="(Logged out) Header, go to Marketplace">Marketplace</a>
              </li>

              <li class="d-block d-lg-flex flex-lg-nowrap flex-lg-items-center border-bottom border-lg-bottom-0 mr-0 mr-lg-3 edge-item-fix position-relative flex-wrap flex-justify-between d-flex flex-items-center ">
                <details class="HeaderMenu-details details-overlay details-reset width-full">
                  <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
                    Pricing
                    <svg x="0px" y="0px" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative">
                       <path d="M1,1l6.2,6L13,1"></path>
                    </svg>
                  </summary>

                  <div class="dropdown-menu flex-auto rounded-1 bg-white px-0 pt-2 pb-4 mt-0 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
                    <a href="/pricing" class="pb-2 lh-condensed-ultra d-block link-gray-dark no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Pricing">Plans <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a>

                    <ul class="list-style-none mb-3">
                      <li class="edge-item-fix"><a href="/pricing#feature-comparison" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Compare plans">Compare plans</a></li>
                      <li class="edge-item-fix"><a href="https://enterprise.github.com/contact" class="py-2 lh-condensed-ultra d-block link-gray no-underline f5" data-ga-click="(Logged out) Header, go to Contact Sales">Contact Sales</a></li>
                    </ul>

                    <ul class="list-style-none mb-0 border-lg-top pt-lg-3">
                      <li class="edge-item-fix"><a href="/nonprofit" class="py-2 lh-condensed-ultra d-block no-underline link-gray-dark no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Nonprofits">Nonprofit <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://education.github.com" class="py-2 pb-0 lh-condensed-ultra d-block no-underline link-gray-dark no-underline h5 Bump-link--hover"  data-ga-click="(Logged out) Header, go to Education">Education <span class="Bump-link-symbol float-right text-normal text-gray-light">&rarr;</span></a></li>
                    </ul>
                  </div>
                </details>
              </li>
          </ul>
        </nav>

      <div class="d-lg-flex flex-items-center px-3 px-lg-0 text-center text-lg-left">
          <div class="d-lg-flex mb-3 mb-lg-0">
            <div class="header-search flex-self-stretch flex-lg-self-auto mr-0 mr-lg-3 mb-3 mb-lg-0 scoped-search site-scoped-search js-site-search position-relative js-jump-to"
  role="combobox"
  aria-owns="jump-to-results"
  aria-label="Search or jump to"
  aria-haspopup="listbox"
  aria-expanded="false"
>
  <div class="position-relative">
    <!-- '"` --><!-- </textarea></xmp> --></option></form><form class="js-site-search-form" role="search" aria-label="Site" data-scope-type="Repository" data-scope-id="44137852" data-scoped-search-url="/apple/cups/search" data-unscoped-search-url="/search" action="/apple/cups/search" accept-charset="UTF-8" method="get"><input name="utf8" type="hidden" value="&#x2713;" />
      <label class="form-control input-sm header-search-wrapper p-0 header-search-wrapper-jump-to position-relative d-flex flex-justify-between flex-items-center js-chromeless-input-container">
        <input type="text"
          class="form-control input-sm header-search-input jump-to-field js-jump-to-field js-site-search-focus js-site-search-field is-clearable"
          data-hotkey="s,/"
          name="q"
          value=""
          placeholder="Search"
          data-unscoped-placeholder="Search GitHub"
          data-scoped-placeholder="Search"
          autocapitalize="off"
          aria-autocomplete="list"
          aria-controls="jump-to-results"
          aria-label="Search"
          data-jump-to-suggestions-path="/_graphql/GetSuggestedNavigationDestinations#csrf-token=0QmMSuZHm84cPNVGQ7RYAXgPRiUfOcjNOC1vXOhVnyzm2B3JSHz1fcIJBZxMAKWUiBXjlVwLkcHLJPZy2gy+mg=="
          spellcheck="false"
          autocomplete="off"
          >
          <input type="hidden" class="js-site-search-type-field" name="type" >
            <img src="https://github.githubassets.com/images/search-key-slash.svg" alt="" class="mr-2 header-search-key-slash">

            <div class="Box position-absolute overflow-hidden d-none jump-to-suggestions js-jump-to-suggestions-container">
              
<ul class="d-none js-jump-to-suggestions-template-container">
  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-suggestion" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 12 16" version="1.1" role="img"><path fill-rule="evenodd" d="M4 9H3V8h1v1zm0-3H3v1h1V6zm0-2H3v1h1V4zm0-2H3v1h1V2zm8-1v12c0 .55-.45 1-1 1H6v2l-1.5-1.5L3 16v-2H1c-.55 0-1-.45-1-1V1c0-.55.45-1 1-1h10c.55 0 1 .45 1 1zm-1 10H1v2h2v-1h3v1h5v-2zm0-10H2v9h9V1z"/></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 15 16" version="1.1" role="img"><path fill-rule="evenodd" d="M10 12h3V2h-3v10zm-4-2h3V2H6v8zm-4 4h3V2H2v12zm-1 1h13V1H1v14zM14 0H1a1 1 0 00-1 1v14a1 1 0 001 1h13a1 1 0 001-1V1a1 1 0 00-1-1z"/></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M15.7 13.3l-3.81-3.83A5.93 5.93 0 0013 6c0-3.31-2.69-6-6-6S1 2.69 1 6s2.69 6 6 6c1.3 0 2.48-.41 3.47-1.11l3.83 3.81c.19.2.45.3.7.3.25 0 .52-.09.7-.3a.996.996 0 000-1.41v.01zM7 10.7c-2.59 0-4.7-2.11-4.7-4.7 0-2.59 2.11-4.7 4.7-4.7 2.59 0 4.7 2.11 4.7 4.7 0 2.59-2.11 4.7-4.7 4.7z"/></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 bg-gray px-1 text-gray-light ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 bg-gray px-1 text-gray-light ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

</ul>

<ul class="d-none js-jump-to-no-results-template-container">
  <li class="d-flex flex-justify-center flex-items-center f5 d-none js-jump-to-suggestion p-2">
    <span class="text-gray">No suggested jump to results</span>
  </li>
</ul>

<ul id="jump-to-results" role="listbox" class="p-0 m-0 js-navigation-container jump-to-suggestions-results-container js-jump-to-suggestions-results-container">
  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-scoped-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 12 16" version="1.1" role="img"><path fill-rule="evenodd" d="M4 9H3V8h1v1zm0-3H3v1h1V6zm0-2H3v1h1V4zm0-2H3v1h1V2zm8-1v12c0 .55-.45 1-1 1H6v2l-1.5-1.5L3 16v-2H1c-.55 0-1-.45-1-1V1c0-.55.45-1 1-1h10c.55 0 1 .45 1 1zm-1 10H1v2h2v-1h3v1h5v-2zm0-10H2v9h9V1z"/></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 15 16" version="1.1" role="img"><path fill-rule="evenodd" d="M10 12h3V2h-3v10zm-4-2h3V2H6v8zm-4 4h3V2H2v12zm-1 1h13V1H1v14zM14 0H1a1 1 0 00-1 1v14a1 1 0 001 1h13a1 1 0 001-1V1a1 1 0 00-1-1z"/></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M15.7 13.3l-3.81-3.83A5.93 5.93 0 0013 6c0-3.31-2.69-6-6-6S1 2.69 1 6s2.69 6 6 6c1.3 0 2.48-.41 3.47-1.11l3.83 3.81c.19.2.45.3.7.3.25 0 .52-.09.7-.3a.996.996 0 000-1.41v.01zM7 10.7c-2.59 0-4.7-2.11-4.7-4.7 0-2.59 2.11-4.7 4.7-4.7 2.59 0 4.7 2.11 4.7 4.7 0 2.59-2.11 4.7-4.7 4.7z"/></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 bg-gray px-1 text-gray-light ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 bg-gray px-1 text-gray-light ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-global-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 12 16" version="1.1" role="img"><path fill-rule="evenodd" d="M4 9H3V8h1v1zm0-3H3v1h1V6zm0-2H3v1h1V4zm0-2H3v1h1V2zm8-1v12c0 .55-.45 1-1 1H6v2l-1.5-1.5L3 16v-2H1c-.55 0-1-.45-1-1V1c0-.55.45-1 1-1h10c.55 0 1 .45 1 1zm-1 10H1v2h2v-1h3v1h5v-2zm0-10H2v9h9V1z"/></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 15 16" version="1.1" role="img"><path fill-rule="evenodd" d="M10 12h3V2h-3v10zm-4-2h3V2H6v8zm-4 4h3V2H2v12zm-1 1h13V1H1v14zM14 0H1a1 1 0 00-1 1v14a1 1 0 001 1h13a1 1 0 001-1V1a1 1 0 00-1-1z"/></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M15.7 13.3l-3.81-3.83A5.93 5.93 0 0013 6c0-3.31-2.69-6-6-6S1 2.69 1 6s2.69 6 6 6c1.3 0 2.48-.41 3.47-1.11l3.83 3.81c.19.2.45.3.7.3.25 0 .52-.09.7-.3a.996.996 0 000-1.41v.01zM7 10.7c-2.59 0-4.7-2.11-4.7-4.7 0-2.59 2.11-4.7 4.7-4.7 2.59 0 4.7 2.11 4.7 4.7 0 2.59-2.11 4.7-4.7 4.7z"/></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 bg-gray px-1 text-gray-light ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 bg-gray px-1 text-gray-light ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>


</ul>

            </div>
      </label>
</form>  </div>
</div>

          </div>

        <a href="/login?return_to=%2Fapple%2Fcups%2Freleases"
          class="HeaderMenu-link no-underline mr-3"
          data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="e4e34255c8485d6915d1448a86ea9723c3b2e92e9576e71845af735870da8d70"
          data-ga-click="(Logged out) Header, clicked Sign in, text:sign-in">
          Sign&nbsp;in
        </a>
          <a href="/join?source=header-repo&amp;source_repo=apple%2Fcups"
            class="HeaderMenu-link d-inline-block no-underline border border-gray-dark rounded-1 px-2 py-1"
            data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="e4e34255c8485d6915d1448a86ea9723c3b2e92e9576e71845af735870da8d70"
            data-ga-click="(Logged out) Header, clicked Sign up, text:sign-up">
            Sign&nbsp;up
          </a>
      </div>
    </div>
  </div>
</header>

  </div>

  <div id="start-of-content" class="show-on-focus"></div>


    <div id="js-flash-container">

</div>



  <div class="application-main " data-commit-hovercards-enabled>
        <div itemscope itemtype="http://schema.org/SoftwareSourceCode" class="">
    <main  >
      


  



  









  <div class=" pagehead repohead readability-menu experiment-repo-nav pt-0 pt-lg-4 ">
    <div class="repohead-details-container clearfix container-lg p-responsive d-none d-lg-block">

      <ul class="pagehead-actions">




  <li>
    
  <a class="tooltipped tooltipped-s btn btn-sm btn-with-count" aria-label="You must be signed in to watch a repository" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;notification subscription menu watch&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="55e61ce807520942724d37d48d3d19c3da4baeecb2ec89bce4f6da0ceb91a62d" href="/login?return_to=%2Fapple%2Fcups">
    <svg class="octicon octicon-eye v-align-text-bottom" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8.06 2C3 2 0 8 0 8s3 6 8.06 6C13 14 16 8 16 8s-3-6-7.94-6zM8 12c-2.2 0-4-1.78-4-4 0-2.2 1.8-4 4-4 2.22 0 4 1.8 4 4 0 2.22-1.78 4-4 4zm2-4c0 1.11-.89 2-2 2-1.11 0-2-.89-2-2 0-1.11.89-2 2-2 1.11 0 2 .89 2 2z"/></svg>
    Watch
</a>    <a class="social-count" href="/apple/cups/watchers"
       aria-label="88 users are watching this repository">
      88
    </a>

  </li>

  <li>
        <a class="btn btn-sm btn-with-count tooltipped tooltipped-s" aria-label="You must be signed in to star a repository" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;star button&quot;,&quot;repository_id&quot;:44137852,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f9704a715be46a567479973573477a583501a1976f99deb0ccdd0b1ed3b74e5b" href="/login?return_to=%2Fapple%2Fcups">
      <svg aria-label="star" height="16" class="octicon octicon-star v-align-text-bottom" viewBox="0 0 14 16" version="1.1" width="14" role="img"><path fill-rule="evenodd" d="M14 6l-4.9-.64L7 1 4.9 5.36 0 6l3.6 3.26L2.67 14 7 11.67 11.33 14l-.93-4.74L14 6z"/></svg>

      Star
</a>
    <a class="social-count js-social-count" href="/apple/cups/stargazers"
      aria-label="883 users starred this repository">
      883
    </a>

  </li>

  <li>
      <a class="btn btn-sm btn-with-count tooltipped tooltipped-s" aria-label="You must be signed in to fork a repository" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;repo details fork button&quot;,&quot;repository_id&quot;:44137852,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="b527b2fea0cd8e94870047972acd87358cf6a735da254b00eb05c15d676f7503" href="/login?return_to=%2Fapple%2Fcups">
        <svg class="octicon octicon-repo-forked v-align-text-bottom" viewBox="0 0 10 16" version="1.1" width="10" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8 1a1.993 1.993 0 00-1 3.72V6L5 8 3 6V4.72A1.993 1.993 0 002 1a1.993 1.993 0 00-1 3.72V6.5l3 3v1.78A1.993 1.993 0 005 15a1.993 1.993 0 001-3.72V9.5l3-3V4.72A1.993 1.993 0 008 1zM2 4.2C1.34 4.2.8 3.65.8 3c0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zm3 10c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zm3-10c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2z"/></svg>
        Fork
</a>
    <a href="/apple/cups/network/members" class="social-count"
       aria-label="260 users forked this repository">
      260
    </a>
  </li>
</ul>

      <h1 class="public ">
    <svg class="octicon octicon-repo" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M4 9H3V8h1v1zm0-3H3v1h1V6zm0-2H3v1h1V4zm0-2H3v1h1V2zm8-1v12c0 .55-.45 1-1 1H6v2l-1.5-1.5L3 16v-2H1c-.55 0-1-.45-1-1V1c0-.55.45-1 1-1h10c.55 0 1 .45 1 1zm-1 10H1v2h2v-1h3v1h5v-2zm0-10H2v9h9V1z"/></svg>
  <span class="author" itemprop="author"><a class="url fn" rel="author" data-hovercard-type="organization" data-hovercard-url="/orgs/apple/hovercard" href="/apple">apple</a></span><!--
--><span class="path-divider">/</span><!--
--><strong itemprop="name"><a data-pjax="#js-repo-pjax-container" href="/apple/cups">cups</a></strong>
  

</h1>

    </div>
    
<nav class="hx_reponav reponav js-repo-nav js-sidenav-container-pjax container-lg p-responsive d-none d-lg-block"
     itemscope
     itemtype="http://schema.org/BreadcrumbList"
    aria-label="Repository"
     data-pjax="#js-repo-pjax-container">

  <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
    <a class="js-selected-navigation-item selected reponav-item" itemprop="url" data-hotkey="g c" aria-current="page" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches repo_packages /apple/cups" href="/apple/cups">
      <div class="d-inline"><svg class="octicon octicon-code" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M9.5 3L8 4.5 11.5 8 8 11.5 9.5 13 14 8 9.5 3zm-5 0L0 8l4.5 5L6 11.5 2.5 8 6 4.5 4.5 3z"/></svg></div>
      <span itemprop="name">Code</span>
      <meta itemprop="position" content="1">
</a>  </span>

    <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
      <a itemprop="url" data-hotkey="g i" class="js-selected-navigation-item reponav-item" data-selected-links="repo_issues repo_labels repo_milestones /apple/cups/issues" href="/apple/cups/issues">
        <div class="d-inline"><svg class="octicon octicon-issue-opened" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7 2.3c3.14 0 5.7 2.56 5.7 5.7s-2.56 5.7-5.7 5.7A5.71 5.71 0 011.3 8c0-3.14 2.56-5.7 5.7-5.7zM7 1C3.14 1 0 4.14 0 8s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm1 3H6v5h2V4zm0 6H6v2h2v-2z"/></svg></div>
        <span itemprop="name">Issues</span>
        <span class="Counter">10</span>
        <meta itemprop="position" content="2">
</a>    </span>

  <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
    <a data-hotkey="g p" data-skip-pjax="true" itemprop="url" class="js-selected-navigation-item reponav-item" data-selected-links="repo_pulls checks /apple/cups/pulls" href="/apple/cups/pulls">
      <div class="d-inline"><svg class="octicon octicon-git-pull-request" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M11 11.28V5c-.03-.78-.34-1.47-.94-2.06C9.46 2.35 8.78 2.03 8 2H7V0L4 3l3 3V4h1c.***********.**********.3.42.31.69v6.28A1.993 1.993 0 0010 15a1.993 1.993 0 001-3.72zm-1 2.92c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zM4 3c0-1.11-.89-2-2-2a1.993 1.993 0 00-1 3.72v6.56A1.993 1.993 0 002 15a1.993 1.993 0 001-3.72V4.72c.59-.34 1-.98 1-1.72zm-.8 10c0 .66-.55 1.2-1.2 1.2-.65 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2zM2 4.2C1.34 4.2.8 3.65.8 3c0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2z"/></svg></div>
      <span itemprop="name">Pull requests</span>
      <span class="Counter">6</span>
      <meta itemprop="position" content="3">
</a>  </span>


    <a data-hotkey="g b" class="js-selected-navigation-item reponav-item" data-selected-links="repo_projects new_repo_project repo_project /apple/cups/projects" href="/apple/cups/projects">
      <div class="d-inline"><svg class="octicon octicon-project" viewBox="0 0 15 16" version="1.1" width="15" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10 12h3V2h-3v10zm-4-2h3V2H6v8zm-4 4h3V2H2v12zm-1 1h13V1H1v14zM14 0H1a1 1 0 00-1 1v14a1 1 0 001 1h13a1 1 0 001-1V1a1 1 0 00-1-1z"/></svg></div>
      Projects
      <span class="Counter" >0</span>
</a>

    <a class="js-selected-navigation-item reponav-item" data-hotkey="g w" data-selected-links="repo_wiki /apple/cups/wiki" href="/apple/cups/wiki">
      <div class="d-inline"><svg class="octicon octicon-book" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M3 5h4v1H3V5zm0 3h4V7H3v1zm0 2h4V9H3v1zm11-5h-4v1h4V5zm0 2h-4v1h4V7zm0 2h-4v1h4V9zm2-6v9c0 .55-.45 1-1 1H9.5l-1 1-1-1H2c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1h5.5l1 1 1-1H15c.55 0 1 .45 1 1zm-8 .5L7.5 3H2v9h6V3.5zm7-.5H9.5l-.5.5V12h6V3z"/></svg></div>
      Wiki
</a>
    <a data-skip-pjax="true" class="js-selected-navigation-item reponav-item" data-selected-links="security alerts policy code_scanning /apple/cups/security/advisories" href="/apple/cups/security/advisories">
      <div class="d-inline"><svg class="octicon octicon-shield" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 2l7-2 7 2v6.02C14 12.69 8.69 16 7 16c-1.69 0-7-3.31-7-7.98V2zm1 .75L7 1l6 1.75v5.268C13 12.104 8.449 15 7 15c-1.449 0-6-2.896-6-6.982V2.75zm1 .75L7 2v12c-1.207 0-5-2.482-5-5.985V3.5z"/></svg></div>
      Security
</a>
    <a class="js-selected-navigation-item reponav-item" data-selected-links="repo_graphs repo_contributors dependency_graph pulse people /apple/cups/pulse" href="/apple/cups/pulse">
      <div class="d-inline"><svg class="octicon octicon-graph" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M16 14v1H0V0h1v14h15zM5 13H3V8h2v5zm4 0H7V3h2v10zm4 0h-2V6h2v7z"/></svg></div>
      Insights
</a>

</nav>

  <div class="reponav-wrapper reponav-small d-lg-none">
  <nav class="reponav js-reponav text-center no-wrap"
       itemscope
       itemtype="http://schema.org/BreadcrumbList">

    <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
      <a class="js-selected-navigation-item selected reponav-item" itemprop="url" aria-current="page" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches repo_packages /apple/cups" href="/apple/cups">
        <span itemprop="name">Code</span>
        <meta itemprop="position" content="1">
</a>    </span>

      <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
        <a itemprop="url" class="js-selected-navigation-item reponav-item" data-selected-links="repo_issues repo_labels repo_milestones /apple/cups/issues" href="/apple/cups/issues">
          <span itemprop="name">Issues</span>
          <span class="Counter">10</span>
          <meta itemprop="position" content="2">
</a>      </span>

    <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
      <a itemprop="url" class="js-selected-navigation-item reponav-item" data-selected-links="repo_pulls checks /apple/cups/pulls" href="/apple/cups/pulls">
        <span itemprop="name">Pull requests</span>
        <span class="Counter">6</span>
        <meta itemprop="position" content="3">
</a>    </span>

      <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
        <a itemprop="url" class="js-selected-navigation-item reponav-item" data-selected-links="repo_projects new_repo_project repo_project /apple/cups/projects" href="/apple/cups/projects">
          <span itemprop="name">Projects</span>
          <span class="Counter">0</span>
          <meta itemprop="position" content="4">
</a>      </span>

      <span itemscope itemtype="http://schema.org/ListItem" itemprop="itemListElement">
        <a itemprop="url" class="js-selected-navigation-item reponav-item" data-selected-links="repo_wiki /apple/cups/wiki" href="/apple/cups/wiki">
          <span itemprop="name">Wiki</span>
          <meta itemprop="position" content="5">
</a>      </span>

      <a itemprop="url" class="js-selected-navigation-item reponav-item" data-selected-links="security alerts policy code_scanning /apple/cups/security/advisories" href="/apple/cups/security/advisories">
        <span itemprop="name">Security</span>
        <meta itemprop="position" content="6">
</a>
      <a class="js-selected-navigation-item reponav-item" data-selected-links="pulse /apple/cups/pulse" href="/apple/cups/pulse">
        Pulse
</a>

  </nav>
</div>


  </div>
<div class="container-lg clearfix new-discussion-timeline experiment-repo-nav  p-responsive">
  <div class="repository-content ">

    
    
        <div class="signup-prompt-bg rounded-1 hide-sm">
      <div class="signup-prompt p-4 text-center mb-4 rounded-1">
        <div class="position-relative">
          <!-- '"` --><!-- </textarea></xmp> --></option></form><form action="/prompt_dismissals/signup" accept-charset="UTF-8" method="post"><input name="utf8" type="hidden" value="&#x2713;" /><input type="hidden" name="_method" value="put" /><input type="hidden" name="authenticity_token" value="kDaX/sF3AoSg/3mGTg7Nw4EgGoxBCvoq8adl0rdW8QfpUQnmpOeq3ExSYkQYQl6UfiRGGQlay9HzoVVnV9pjaQ==" />
            <button type="submit" class="position-absolute top-0 right-0 btn-link link-gray" data-ga-click="(Logged out) Sign up prompt, clicked Dismiss, text:dismiss">
              Dismiss
            </button>
</form>          <h3 class="pt-2">Be notified of new releases</h3>
          <p class="col-8 mx-auto">Create your free GitHub account today to subscribe to this repository for new releases and build software alongside 40 million developers.</p>
          <a class="btn btn-primary" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;releases signup prompt&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;client_id&quot;:null,&quot;originating_request_id&quot;:&quot;E0CB:22348:508D9B:74D7B0:5DFA43C4&quot;,&quot;originating_url&quot;:&quot;https://github.com/apple/cups/releases&quot;,&quot;referrer&quot;:null,&quot;user_id&quot;:null}}" data-hydro-click-hmac="c2a9f870ddf9968cc69782464c68391f9e6ae7646501d13bcfcce68458bf82e5" data-ga-click="(Logged out) Sign up prompt, clicked Sign up, text:sign-up" href="/join?source=prompt-releases">Sign up</a>
        </div>
      </div>
    </div>


  <div class="subnav">
    <div class="d-flex flex-md-row flex-justify-between flex-md-items-center">
  <div class="subnav-links float-left" role="navigation">
    <a class="js-selected-navigation-item selected subnav-item" aria-current="page" data-selected-links="repo_releases /apple/cups/releases" href="/apple/cups/releases">Releases</a>
    <a class="js-selected-navigation-item subnav-item" data-selected-links="repo_tags /apple/cups/tags" href="/apple/cups/tags">Tags</a>
  </div>
</div>


  </div>

  <div class="position-relative border-top clearfix">



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-latest">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">
        <span class="flex-shrink-0 Label Label--outline mb-md-2 mr-2 mr-md-0 Label--outline-green">
            <a class="border-0 Label--outline-green" href="/apple/cups/releases/latest">Latest release</a>
        </span>


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.3.1" class="muted-link css-truncate" title="v2.3.1">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.3.1</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/ed181c24e4b130d2981785d44488bef8cfda5f6d" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>ed181c2</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.3.1">v2.3.1</a>
        </div>

          <span class="d-md-none flex-shrink-0 ml-2 mt-2 mt-md-0 Label Label--outline Label--outline-green">
              <a class="border-0 Label--outline-green" href="/apple/cups/releases/latest">Latest release</a>
          </span>

      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.3.1" class="muted-link css-truncate" title="v2.3.1">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.3.1</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/ed181c24e4b130d2981785d44488bef8cfda5f6d" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>ed181c2</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-12-13T14:51:11Z" class="no-wrap">Dec 13, 2019</relative-time>
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.3.1 is a general bug fix release, including a fix for CVE-2019-2228.  Changes include:</p>
<ul>
<li>Documentation updates (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="504683457" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5661" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5661/hovercard" href="https://github.com/apple/cups/issues/5661">#5661</a>, <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="514973853" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5674" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5674/hovercard" href="https://github.com/apple/cups/issues/5674">#5674</a>, <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="520470189" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5682" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5682/hovercard" href="https://github.com/apple/cups/issues/5682">#5682</a>)</li>
<li>CVE-2019-2228: The <code>ippSetValuetag</code> function did not validate the default<br>
language value.</li>
<li>Fixed a crash bug in the web interface (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="473925613" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5621" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5621/hovercard" href="https://github.com/apple/cups/pull/5621">#5621</a>)</li>
<li>The PPD cache code now looks up page sizes using their dimensions<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="479877680" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5633" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5633/hovercard" href="https://github.com/apple/cups/issues/5633">#5633</a>)</li>
<li>PPD files containing "custom" option keywords did not work (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="486024591" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5639" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5639/hovercard" href="https://github.com/apple/cups/issues/5639">#5639</a>)</li>
<li>Added a workaround for the scheduler's systemd support (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="486034405" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5640" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5640/hovercard" href="https://github.com/apple/cups/issues/5640">#5640</a>)</li>
<li>On Windows, TLS certificates generated on February 29 would likely fail<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="487215972" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5643" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5643/hovercard" href="https://github.com/apple/cups/pull/5643">#5643</a>)</li>
<li>Added a DigestOptions directive for the <code>client.conf</code> file to control whether<br>
MD5-based Digest authentication is allowed (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="489725887" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5647" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5647/hovercard" href="https://github.com/apple/cups/issues/5647">#5647</a>)</li>
<li>Fixed a bug in the handling of printer resource files (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="495269347" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5652" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5652/hovercard" href="https://github.com/apple/cups/issues/5652">#5652</a>)</li>
<li>The libusb-based USB backend now reports an error when the distribution<br>
permissions are wrong (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="500594517" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5658" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5658/hovercard" href="https://github.com/apple/cups/issues/5658">#5658</a>)</li>
<li>Added paint can labels to Dymo driver (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="504754248" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5662" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5662/hovercard" href="https://github.com/apple/cups/issues/5662">#5662</a>)</li>
<li>The <code>ippeveprinter</code> program now supports authentication (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="505829095" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5665" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5665/hovercard" href="https://github.com/apple/cups/issues/5665">#5665</a>)</li>
<li>The <code>ippeveprinter</code> program now advertises DNS-SD services on the correct<br>
interfaces, and provides a way to turn them off (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="505830047" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5666" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5666/hovercard" href="https://github.com/apple/cups/issues/5666">#5666</a>)</li>
<li>The <code>--with-dbusdir</code> option was ignored by the configure script (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="509611838" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5671" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5671/hovercard" href="https://github.com/apple/cups/issues/5671">#5671</a>)</li>
<li>Sandboxed applications were not able to get the default printer (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="519179600" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5676" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5676/hovercard" href="https://github.com/apple/cups/issues/5676">#5676</a>)</li>
<li>Log file access controls were not preserved by <code>cupsctl</code> (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="519230147" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5677" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5677/hovercard" href="https://github.com/apple/cups/pull/5677">#5677</a>)</li>
<li>Default printers set with <code>lpoptions</code> did not work in all cases (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="520415966" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5681" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5681/hovercard" href="https://github.com/apple/cups/issues/5681">#5681</a>,<br>
Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="522065376" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5683" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5683/hovercard" href="https://github.com/apple/cups/issues/5683">#5683</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="523200142" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5684" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5684/hovercard" href="https://github.com/apple/cups/issues/5684">#5684</a>)</li>
<li>Fixed an error in the jobs web interface template (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="529989288" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5694" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5694/hovercard" href="https://github.com/apple/cups/pull/5694">#5694</a>)</li>
<li>Fixed an off-by-one error in <code>ippEnumString</code> (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="532862341" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5695" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5695/hovercard" href="https://github.com/apple/cups/pull/5695">#5695</a>)</li>
<li>Fixed some new compiler warnings (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="534545419" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5700" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5700/hovercard" href="https://github.com/apple/cups/pull/5700">#5700</a>)</li>
<li>Fixed a few issues with the Apple Raster support (rdar://55301114)</li>
<li>The IPP backend did not detect all cases where a job should be retried using<br>
a raster format (rdar://56021091)</li>
<li>Fixed spelling of "fold-accordion".</li>
<li>Fixed the default common name for TLS certificates used by <code>ippeveprinter</code>.</li>
<li>Fixed the option names used for IPP Everywhere finishing options.</li>
<li>Added support for the second roll of the DYMO Twin/DUO label printers.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    open
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3.1/cups-2.3.1-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3.1-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">7.76 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3.1/cups-2.3.1-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3.1-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3.1.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3.1.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.2.13" class="muted-link css-truncate" title="v2.2.13">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.2.13</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/0ad6ac55da2686c6fa24b05bde5e9036fd4db5d1" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>0ad6ac5</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.2.13">v2.2.13</a>
        </div>


      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.2.13" class="muted-link css-truncate" title="v2.2.13">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.2.13</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/0ad6ac55da2686c6fa24b05bde5e9036fd4db5d1" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>0ad6ac5</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-12-13T14:49:55Z" class="no-wrap">Dec 13, 2019</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.2.13...master">
    793 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.2.13 is the last general bug fix release in the 2.2.x series and includes<br>
a fix for CVE-2019-2228.  Changes include:</p>
<ul>
<li>CVE-2019-2228: The <code>ippSetValuetag</code> function did not validate the default<br>
language value.</li>
<li>Added a workaround for the scheduler's systemd support (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="486034405" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5640" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5640/hovercard" href="https://github.com/apple/cups/issues/5640">#5640</a>)</li>
<li>Fixed spelling of "fold-accordion".</li>
<li>Fixed the default common name for TLS certificates used by <code>ippserver</code>.</li>
<li>The libusb-based USB backend now reports an error when the distribution<br>
permissions are wrong (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="500594517" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5658" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5658/hovercard" href="https://github.com/apple/cups/issues/5658">#5658</a>)</li>
<li>Default printers set with <code>lpoptions</code> did not work in all cases (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="520415966" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5681" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5681/hovercard" href="https://github.com/apple/cups/issues/5681">#5681</a>,<br>
Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="522065376" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5683" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5683/hovercard" href="https://github.com/apple/cups/issues/5683">#5683</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="523200142" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5684" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5684/hovercard" href="https://github.com/apple/cups/issues/5684">#5684</a>)</li>
<li>Fixed an off-by-one error in <code>ippEnumString</code> (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="532862341" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5695" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5695/hovercard" href="https://github.com/apple/cups/pull/5695">#5695</a>)</li>
<li>Fixed some new compiler warnings (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="534545419" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5700" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5700/hovercard" href="https://github.com/apple/cups/pull/5700">#5700</a>)</li>
<li>Fixed a few issues with the Apple Raster support (rdar://55301114)</li>
<li>The IPP backend did not detect all cases where a job should be retried using<br>
a raster format (rdar://56021091)</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.13/cups-2.2.13-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.13-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.93 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.13/cups-2.2.13-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.13-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.13.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.13.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.3.0" class="muted-link css-truncate" title="v2.3.0">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.3.0</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/ee6be9cd7e863abf36b9b131fc379d967414aa2e" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>ee6be9c</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.3.0">v2.3.0</a>
        </div>


      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.3.0" class="muted-link css-truncate" title="v2.3.0">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.3.0</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/ee6be9cd7e863abf36b9b131fc379d967414aa2e" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>ee6be9c</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-08-23T18:17:21Z" class="no-wrap">Aug 23, 2019</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.3.0...master">
    65 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.3.0 is now available for download, which adopts the new CUPS license, adds support for IPP presets and finishing templates, fixes a number of bugs and "polish" issues, and includes the new ippeveprinter utility. Changes include:</p>
<ul>
<li>CVE-2019-8696 and CVE-2019-8675: Fixed SNMP buffer overflows (rdar://51685251)</li>
<li>Added a GPL2/LGPL2 exception to the new CUPS license terms.</li>
<li>Documentation updates (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="459594644" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5604" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5604/hovercard" href="https://github.com/apple/cups/issues/5604">#5604</a>)</li>
<li>Localization updates (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="483641898" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5637" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5637/hovercard" href="https://github.com/apple/cups/pull/5637">#5637</a>)</li>
<li>Fixed a bug in the scheduler job cleanup code (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="450279216" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5588" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5588/hovercard" href="https://github.com/apple/cups/pull/5588">#5588</a>)</li>
<li>Fixed builds when there is no TLS library (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="451578186" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5590" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5590/hovercard" href="https://github.com/apple/cups/pull/5590">#5590</a>)</li>
<li>Eliminated some new GCC compiler warnings (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="453328747" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5591" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5591/hovercard" href="https://github.com/apple/cups/pull/5591">#5591</a>)</li>
<li>Removed dead code from the scheduler (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="453329871" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5593" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5593/hovercard" href="https://github.com/apple/cups/pull/5593">#5593</a>)</li>
<li>"make" failed with GZIP options (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="455017934" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5595" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5595/hovercard" href="https://github.com/apple/cups/issues/5595">#5595</a>)</li>
<li>Fixed potential excess logging from the scheduler when removing job files<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="456284406" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5597" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5597/hovercard" href="https://github.com/apple/cups/issues/5597">#5597</a>)</li>
<li>Fixed a NULL pointer dereference bug in <code>httpGetSubField2</code> (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="456534137" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5598" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5598/hovercard" href="https://github.com/apple/cups/issues/5598">#5598</a>)</li>
<li>Added FIPS-140 workarounds for GNU TLS (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="458015929" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5601" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5601/hovercard" href="https://github.com/apple/cups/issues/5601">#5601</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="474117231" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5622" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5622/hovercard" href="https://github.com/apple/cups/pull/5622">#5622</a>)</li>
<li>The scheduler no longer provides a default value for the description<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="459074733" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5603" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5603/hovercard" href="https://github.com/apple/cups/issues/5603">#5603</a>)</li>
<li>The scheduler now logs jobs held for authentication using the error level so<br>
it is clear what happened (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="459594644" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5604" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5604/hovercard" href="https://github.com/apple/cups/issues/5604">#5604</a>)</li>
<li>The <code>lpadmin</code> command did not always update the PPD file for changes to the<br>
<code>cupsIPPSupplies</code> and <code>cupsSNMPSupplies</code> keywords (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="465727195" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5610" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5610/hovercard" href="https://github.com/apple/cups/pull/5610">#5610</a>)</li>
<li>The scheduler now uses both the group's membership list as well as the<br>
various OS-specific membership functions to determine whether a user belongs<br>
to a named group (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="468463678" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5613" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5613/hovercard" href="https://github.com/apple/cups/pull/5613">#5613</a>)</li>
<li>Added USB quirks rule for HP LaserJet 1015 (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="470659313" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5617" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5617/hovercard" href="https://github.com/apple/cups/issues/5617">#5617</a>)</li>
<li>Fixed some PPD parser issues (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="474314074" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5623" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5623/hovercard" href="https://github.com/apple/cups/pull/5623">#5623</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="474317092" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5624" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5624/hovercard" href="https://github.com/apple/cups/pull/5624">#5624</a>)</li>
<li>The IPP parser no longer allows invalid member attributes in collections<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="477979886" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5630" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5630/hovercard" href="https://github.com/apple/cups/issues/5630">#5630</a>)</li>
<li>The configure script now treats the "wheel" group as a potential system<br>
group (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="484535130" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5638" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5638/hovercard" href="https://github.com/apple/cups/pull/5638">#5638</a>)</li>
<li>Fixed a USB printing issue on macOS (rdar://31433931)</li>
<li>Fixed IPP buffer overflow (rdar://50035411)</li>
<li>Fixed memory disclosure issue in the scheduler (rdar://51373853)</li>
<li>Fixed DoS issues in the scheduler (rdar://51373929)</li>
<li>Fixed an issue with unsupported "sides" values in the IPP backend<br>
(rdar://51775322)</li>
<li>The scheduler would restart continuously when idle and printers were not<br>
shared (rdar://52561199)</li>
<li>Fixed an issue with <code>EXPECT !name WITH-VALUE ...</code> tests.</li>
<li>Fixed a command ordering issue in the Zebra ZPL driver.</li>
<li>Fixed a memory leak in <code>ppdOpen</code>.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3.0/cups-2.3.0-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3.0-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">7.75 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3.0/cups-2.3.0-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3.0-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3.0.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3.0.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.2.12" class="muted-link css-truncate" title="v2.2.12">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.2.12</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/c8cb6400e317633f66cdc87884915d54710b53a0" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>c8cb640</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.2.12">v2.2.12</a>
        </div>


      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.2.12" class="muted-link css-truncate" title="v2.2.12">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.2.12</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/c8cb6400e317633f66cdc87884915d54710b53a0" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>c8cb640</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-08-15T22:52:44Z" class="no-wrap">Aug 15, 2019</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.2.12...master">
    793 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.2.12 is now available and includes security, compatibility, and general bug fixes. Changes include:</p>
<ul>
<li>CVE-2019-8696 and CVE-2019-8675: Fixed SNMP buffer overflows (rdar://51685251)</li>
<li>The <code>cupsctl</code> command now prevents setting "cups-files.conf" directives<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="413829254" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5530" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5530/hovercard" href="https://github.com/apple/cups/issues/5530">#5530</a>)</li>
<li>Updated the systemd service file for cupsd (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="426479041" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5551" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5551/hovercard" href="https://github.com/apple/cups/pull/5551">#5551</a>)</li>
<li>The <code>cupsCheckDestSupported</code> function did not check octetString values<br>
correctly (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="429820862" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5557" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5557/hovercard" href="https://github.com/apple/cups/pull/5557">#5557</a>)</li>
<li>The scheduler did not encode octetString values like "job-password" correctly<br>
for the print filters (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="429827127" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5558" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5558/hovercard" href="https://github.com/apple/cups/pull/5558">#5558</a>)</li>
<li>Restored minimal support for the <code>Emulators</code> keyword in PPD files to allow<br>
old Samsung printer drivers to continue to work (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="432214569" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5562" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5562/hovercard" href="https://github.com/apple/cups/issues/5562">#5562</a>)</li>
<li>Timed out job submission now yields an error (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="437390564" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5570" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5570/hovercard" href="https://github.com/apple/cups/issues/5570">#5570</a>)</li>
<li>The footer in the web interface covered some content on small displays<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="440365801" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5574" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5574/hovercard" href="https://github.com/apple/cups/issues/5574">#5574</a>)</li>
<li>The libusb-based USB backend now enforces read limits, improving print speed<br>
in many cases (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="445315055" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5583" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5583/hovercard" href="https://github.com/apple/cups/issues/5583">#5583</a>)</li>
<li>Fixed some compatibility issues with old releases of CUPS (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="449361069" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5587" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5587/hovercard" href="https://github.com/apple/cups/issues/5587">#5587</a>)</li>
<li>Fixed a bug in the scheduler job cleanup code (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="450279216" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5588" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5588/hovercard" href="https://github.com/apple/cups/pull/5588">#5588</a>)</li>
<li>"make" failed with GZIP options (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="455017934" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5595" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5595/hovercard" href="https://github.com/apple/cups/issues/5595">#5595</a>)</li>
<li>Added FIPS-140 workarounds for GNU TLS (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="458015929" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5601" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5601/hovercard" href="https://github.com/apple/cups/issues/5601">#5601</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="474117231" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5622" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5622/hovercard" href="https://github.com/apple/cups/pull/5622">#5622</a>)</li>
<li>The scheduler no longer provides a default value for the description<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="459074733" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5603" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5603/hovercard" href="https://github.com/apple/cups/issues/5603">#5603</a>)</li>
<li>The <code>lpadmin</code> command did not always update the PPD file for changes to the<br>
<code>cupsIPPSupplies</code> and <code>cupsSNMPSupplies</code> keywords (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="465727195" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5610" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5610/hovercard" href="https://github.com/apple/cups/pull/5610">#5610</a>)</li>
<li>The scheduler now uses both the group's membership list as well as the<br>
various OS-specific membership functions to determine whether a user belongs<br>
to a named group (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="468463678" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5613" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5613/hovercard" href="https://github.com/apple/cups/pull/5613">#5613</a>)</li>
<li>Added USB quirks rule for HP LaserJet 1015 (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="470659313" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5617" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5617/hovercard" href="https://github.com/apple/cups/issues/5617">#5617</a>)</li>
<li>Fixed some PPD parser issues (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="474314074" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5623" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5623/hovercard" href="https://github.com/apple/cups/pull/5623">#5623</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="474317092" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5624" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5624/hovercard" href="https://github.com/apple/cups/pull/5624">#5624</a>)</li>
<li>The IPP parser no longer allows invalid member attributes in collections<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="477979886" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5630" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5630/hovercard" href="https://github.com/apple/cups/issues/5630">#5630</a>)</li>
<li>Fixed IPP buffer overflow (rdar://50035411)</li>
<li>Fixed memory disclosure issue in the scheduler (rdar://51373853)</li>
<li>Fixed DoS issues in the scheduler (rdar://51373929)</li>
<li>The scheduler would restart continuously when idle and printers were not<br>
shared (rdar://52561199)</li>
<li>Fixed a command ordering issue in the Zebra ZPL driver.</li>
<li>Fixed a memory leak in <code>ppdOpen</code>.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.12/cups-2.2.12-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.12-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.93 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.12/cups-2.2.12-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.12-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.12.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.12.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-prerelease">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">
        <span class="flex-shrink-0 Label Label--outline mb-md-2 mr-2 mr-md-0 Label--prerelease">
            Pre-release
        </span>


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.3rc1" class="muted-link css-truncate" title="v2.3rc1">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.3rc1</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/4d03c4ca90d8c1c0d5303427858e537fd3c415e4" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>4d03c4c</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.3rc1">v2.3rc1</a>
        </div>

          <span class="d-md-none flex-shrink-0 ml-2 mt-2 mt-md-0 Label Label--outline Label--prerelease">
              Pre-release
          </span>

      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.3rc1" class="muted-link css-truncate" title="v2.3rc1">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.3rc1</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/4d03c4ca90d8c1c0d5303427858e537fd3c415e4" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>4d03c4c</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-05-21T17:07:50Z" class="no-wrap">May 21, 2019</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.3rc1...master">
    122 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.3rc1 is now available for download.  This is the first release candidate for CUPS 2.3.0 which adopts the new CUPS license, adds support for IPP presets and finishing templates, and fixes a number of bugs and "polish" issues.  This beta also includes the new ippeveprinter utility. Changes include:</p>
<ul>
<li>The <code>cups-config</code> script no longer adds extra libraries when linking against<br>
shared libraries (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="303656158" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5261" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5261/hovercard" href="https://github.com/apple/cups/pull/5261">#5261</a>)</li>
<li>The supplied example print documents have been optimized for size<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="413810404" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5529" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5529/hovercard" href="https://github.com/apple/cups/pull/5529">#5529</a>)</li>
<li>The <code>cupsctl</code> command now prevents setting "cups-files.conf" directives<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="413829254" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5530" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5530/hovercard" href="https://github.com/apple/cups/issues/5530">#5530</a>)</li>
<li>The "forbidden" message in the web interface is now explained (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="424534348" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5547" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5547/hovercard" href="https://github.com/apple/cups/issues/5547">#5547</a>)</li>
<li>The footer in the web interface covered some content on small displays<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="440365801" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5574" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5574/hovercard" href="https://github.com/apple/cups/issues/5574">#5574</a>)</li>
<li>The libusb-based USB backend now enforces read limits, improving print speed<br>
in many cases (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="445315055" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5583" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5583/hovercard" href="https://github.com/apple/cups/issues/5583">#5583</a>)</li>
<li>The <code>ippeveprinter</code> command now looks for print commands in the "command"<br>
subdirectory.</li>
<li>The <code>ipptool</code> command now supports <code>$date-current</code> and <code>$date-start</code> variables<br>
to insert the current and starting date and time values, as well as ISO-8601<br>
relative time values such as "PT30S" for 30 seconds in the future.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3rc1/cups-2.3rc1-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3rc1-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">7.56 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3rc1/cups-2.3rc1-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3rc1-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3rc1.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3rc1.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-prerelease">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">
        <span class="flex-shrink-0 Label Label--outline mb-md-2 mr-2 mr-md-0 Label--prerelease">
            Pre-release
        </span>


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.3b8" class="muted-link css-truncate" title="v2.3b8">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.3b8</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/0450ddd4f003de8aa8d8e7a54d181a992607d031" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>0450ddd</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.3b8">v2.3b8</a>
        </div>

          <span class="d-md-none flex-shrink-0 ml-2 mt-2 mt-md-0 Label Label--outline Label--prerelease">
              Pre-release
          </span>

      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.3b8" class="muted-link css-truncate" title="v2.3b8">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.3b8</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/0450ddd4f003de8aa8d8e7a54d181a992607d031" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>0450ddd</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-05-02T17:10:16Z" class="no-wrap">May 2, 2019</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.3b8...master">
    165 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.3b8 is now available for download.  This is the eighth beta of the CUPS 2.3 series which adopts the new CUPS license, adds support for IPP presets and finishing templates, and fixes a number of bugs and "polish" issues.  This beta also includes the new ippeveprinter utility.  Changes include:</p>
<ul>
<li>Media size matching now uses a tolerance of 0.5mm (rdar://********)</li>
<li>The lpadmin command would hang with a bad PPD file (rdar://********)</li>
<li>Fixed a potential crash bug in cups-driverd (rdar://46625579)</li>
<li>Fixed a performance regression with large PPDs (rdar://47040759)</li>
<li>Fixed a memory reallocation bug in HTTP header value expansion<br>
(rdar://problem/50000749)</li>
<li>Timed out job submission now yields an error (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="437390564" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5570" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5570/hovercard" href="https://github.com/apple/cups/issues/5570">#5570</a>)</li>
<li>Restored minimal support for the <code>Emulators</code> keyword in PPD files to allow<br>
old Samsung printer drivers to continue to work (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="432214569" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5562" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5562/hovercard" href="https://github.com/apple/cups/issues/5562">#5562</a>)</li>
<li>The scheduler did not encode octetString values like "job-password" correctly<br>
for the print filters (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="429827127" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5558" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5558/hovercard" href="https://github.com/apple/cups/pull/5558">#5558</a>)</li>
<li>The <code>cupsCheckDestSupported</code> function did not check octetString values<br>
correctly (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="429820862" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5557" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5557/hovercard" href="https://github.com/apple/cups/pull/5557">#5557</a>)</li>
<li>Added support for <code>UserAgentTokens</code> directive in "client.conf" (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="429062399" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5555" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5555/hovercard" href="https://github.com/apple/cups/issues/5555">#5555</a>)</li>
<li>Updated the systemd service file for cupsd (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="426479041" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5551" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5551/hovercard" href="https://github.com/apple/cups/pull/5551">#5551</a>)</li>
<li>The <code>ippValidateAttribute</code> function did not catch all instances of invalid<br>
UTF-8 strings (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="406851979" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5509" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5509/hovercard" href="https://github.com/apple/cups/issues/5509">#5509</a>)</li>
<li>Fixed an issue with the self-signed certificates generated by GNU TLS<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="405881346" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5506" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5506/hovercard" href="https://github.com/apple/cups/issues/5506">#5506</a>)</li>
<li>Fixed a potential memory leak when reading at the end of a file (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="397128929" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5473" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5473/hovercard" href="https://github.com/apple/cups/pull/5473">#5473</a>)</li>
<li>Fixed potential unaligned accesses in the string pool (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="397129335" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5474" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5474/hovercard" href="https://github.com/apple/cups/pull/5474">#5474</a>)</li>
<li>Fixed a potential memory leak when loading a PPD file (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="397129544" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5475" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5475/hovercard" href="https://github.com/apple/cups/pull/5475">#5475</a>)</li>
<li>Added a USB quirks rule for the Lexmark E120n (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="398002110" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5478" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5478/hovercard" href="https://github.com/apple/cups/issues/5478">#5478</a>)</li>
<li>Updated the USB quirks rule for Zebra label printers (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="357733286" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5395" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5395/hovercard" href="https://github.com/apple/cups/issues/5395">#5395</a>)</li>
<li>Fixed a compile error on Linux (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="400297392" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5483" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5483/hovercard" href="https://github.com/apple/cups/issues/5483">#5483</a>)</li>
<li>The lpadmin command, web interface, and scheduler all queried an IPP<br>
Everywhere printer differently, resulting in different PPDs for the same<br>
printer (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="400475471" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5484" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5484/hovercard" href="https://github.com/apple/cups/issues/5484">#5484</a>)</li>
<li>The web interface no longer provides access to the log files (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="408923646" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5513" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5513/hovercard" href="https://github.com/apple/cups/pull/5513">#5513</a>)</li>
<li>Non-Kerberized printing to Windows via IPP was broken (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="409779414" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5515" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5515/hovercard" href="https://github.com/apple/cups/issues/5515">#5515</a>)</li>
<li>Eliminated use of private headers and some deprecated macOS APIs (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="410260107" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5516" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5516/hovercard" href="https://github.com/apple/cups/issues/5516">#5516</a>)</li>
<li>The scheduler no longer stops a printer if an error occurs when a job is<br>
canceled or aborted (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="410355270" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5517" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5517/hovercard" href="https://github.com/apple/cups/issues/5517">#5517</a>)</li>
<li>Added a USB quirks rule for the DYMO 450 Turbo (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="411711197" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5521" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5521/hovercard" href="https://github.com/apple/cups/issues/5521">#5521</a>)</li>
<li>Added a USB quirks rule for Xerox printers (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="411753258" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5523" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5523/hovercard" href="https://github.com/apple/cups/pull/5523">#5523</a>)</li>
<li>The scheduler's self-signed certificate did not include all of the alternate<br>
names for the server when using GNU TLS (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="412016449" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5525" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5525/hovercard" href="https://github.com/apple/cups/issues/5525">#5525</a>)</li>
<li>Fixed compiler warnings with newer versions of GCC (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="414042934" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5532" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5532/hovercard" href="https://github.com/apple/cups/issues/5532">#5532</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5533" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5533/hovercard" href="https://github.com/apple/cups/issues/5533">#5533</a>)</li>
<li>Fixed some PPD caching and IPP Everywhere PPD accounting/password bugs<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5535" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5535/hovercard" href="https://github.com/apple/cups/pull/5535">#5535</a>)</li>
<li>Fixed <code>PreserveJobHistory</code> bug with time values (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5538" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5538/hovercard" href="https://github.com/apple/cups/issues/5538">#5538</a>)</li>
<li>The scheduler no longer advertises the HTTP methods it supports (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5540" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5540/hovercard" href="https://github.com/apple/cups/issues/5540">#5540</a>)</li>
<li>Localization updates (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="389910117" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5461" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5461/hovercard" href="https://github.com/apple/cups/pull/5461">#5461</a>, Issues <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="396109475" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5471" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5471/hovercard" href="https://github.com/apple/cups/pull/5471">#5471</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="399515079" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5481" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5481/hovercard" href="https://github.com/apple/cups/pull/5481">#5481</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401477553" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5486" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5486/hovercard" href="https://github.com/apple/cups/pull/5486">#5486</a>,<br>
Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401560266" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5489" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5489/hovercard" href="https://github.com/apple/cups/pull/5489">#5489</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401580756" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5491" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5491/hovercard" href="https://github.com/apple/cups/issues/5491">#5491</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401583167" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5492" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5492/hovercard" href="https://github.com/apple/cups/pull/5492">#5492</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401587120" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5493" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5493/hovercard" href="https://github.com/apple/cups/pull/5493">#5493</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401591890" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5494" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5494/hovercard" href="https://github.com/apple/cups/pull/5494">#5494</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="401976167" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5495" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5495/hovercard" href="https://github.com/apple/cups/issues/5495">#5495</a>,<br>
Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="402516307" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5497" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5497/hovercard" href="https://github.com/apple/cups/issues/5497">#5497</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="402552206" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5499" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5499/hovercard" href="https://github.com/apple/cups/pull/5499">#5499</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="402552661" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5500" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5500/hovercard" href="https://github.com/apple/cups/pull/5500">#5500</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="402553767" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5501" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5501/hovercard" href="https://github.com/apple/cups/pull/5501">#5501</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="403396003" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5504" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5504/hovercard" href="https://github.com/apple/cups/pull/5504">#5504</a>)</li>
<li>The scheduler did not always idle exit as quickly as it could.</li>
<li>Added a new <code>ippeveprinter</code> command based on the old ippserver sample code.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3b8/cups-2.3b8-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3b8-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.89 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3b8/cups-2.3b8-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3b8-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3b8.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3b8.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.2.11" class="muted-link css-truncate" title="v2.2.11">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.2.11</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/e4a0aa86c96b5ddda3770c46709f4dee9a59071f" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>e4a0aa8</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.2.11">v2.2.11</a>
        </div>


      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.2.11" class="muted-link css-truncate" title="v2.2.11">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.2.11</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/e4a0aa86c96b5ddda3770c46709f4dee9a59071f" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>e4a0aa8</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2019-03-22T20:07:02Z" class="no-wrap">Mar 22, 2019</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.2.11...master">
    793 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.2.11 is a bug fix release that addresses issues in the scheduler,<br>
IPP Everywhere support, CUPS library, and USB printer support.  Changes include:</p>
<ul>
<li>Running ppdmerge with the same input and output filenames did not work as<br>
advertised (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="389283806" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5455" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5455/hovercard" href="https://github.com/apple/cups/issues/5455">#5455</a>)</li>
<li>Fixed a potential memory leak when reading at the end of a file (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="397128929" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5473" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5473/hovercard" href="https://github.com/apple/cups/pull/5473">#5473</a>)</li>
<li>Fixed potential unaligned accesses in the string pool (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="397129335" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5474" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5474/hovercard" href="https://github.com/apple/cups/pull/5474">#5474</a>)</li>
<li>Fixed a potential memory leak when loading a PPD file (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="397129544" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5475" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5475/hovercard" href="https://github.com/apple/cups/pull/5475">#5475</a>)</li>
<li>Added a USB quirks rule for the Lexmark E120n (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="398002110" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5478" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5478/hovercard" href="https://github.com/apple/cups/issues/5478">#5478</a>)</li>
<li>Updated the USB quirks rule for Zebra label printers (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="357733286" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5395" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5395/hovercard" href="https://github.com/apple/cups/issues/5395">#5395</a>)</li>
<li>Fixed a compile error on Linux (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="400297392" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5483" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5483/hovercard" href="https://github.com/apple/cups/issues/5483">#5483</a>)</li>
<li>The lpadmin command, web interface, and scheduler all queried an IPP<br>
Everywhere printer differently, resulting in different PPDs for the same<br>
printer (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="400475471" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5484" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5484/hovercard" href="https://github.com/apple/cups/issues/5484">#5484</a>)</li>
<li>Fixed an issue with the self-signed certificates generated by GNU TLS<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="405881346" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5506" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5506/hovercard" href="https://github.com/apple/cups/issues/5506">#5506</a>)</li>
<li>The <code>ippValidateAttribute</code> function did not catch all instances of invalid<br>
UTF-8 strings (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="406851979" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5509" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5509/hovercard" href="https://github.com/apple/cups/issues/5509">#5509</a>)</li>
<li>Non-Kerberized printing to Windows via IPP was broken (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="409779414" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5515" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5515/hovercard" href="https://github.com/apple/cups/issues/5515">#5515</a>)</li>
<li>The scheduler no longer stops a printer if an error occurs when a job is<br>
canceled or aborted (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="410355270" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5517" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5517/hovercard" href="https://github.com/apple/cups/issues/5517">#5517</a>)</li>
<li>Added a USB quirks rule for the DYMO 450 Turbo (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="411711197" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5521" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5521/hovercard" href="https://github.com/apple/cups/issues/5521">#5521</a>)</li>
<li>Added a USB quirks rule for Xerox printers (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="411753258" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5523" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5523/hovercard" href="https://github.com/apple/cups/pull/5523">#5523</a>)</li>
<li>The scheduler's self-signed certificate did not include all of the alternate<br>
names for the server when using GNU TLS (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="412016449" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5525" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5525/hovercard" href="https://github.com/apple/cups/issues/5525">#5525</a>)</li>
<li>Fixed compiler warnings with newer versions of GCC (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="414042934" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5532" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5532/hovercard" href="https://github.com/apple/cups/issues/5532">#5532</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5533" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5533/hovercard" href="https://github.com/apple/cups/issues/5533">#5533</a>)</li>
<li>Fixed some PPD caching and IPP Everywhere PPD accounting/password bugs<br>
(Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5535" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5535/hovercard" href="https://github.com/apple/cups/pull/5535">#5535</a>)</li>
<li>Fixed <code>PreserveJobHistory</code> bug with time values (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5538" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5538/hovercard" href="https://github.com/apple/cups/issues/5538">#5538</a>)</li>
<li>Media size matching now uses a tolerance of 0.5mm (rdar://********)</li>
<li>The lpadmin command would hang with a bad PPD file (rdar://********)</li>
<li>Fixed a potential crash bug in cups-driverd (rdar://46625579)</li>
<li>Fixed a performance regression with large PPDs (rdar://47040759)</li>
<li>The scheduler did not always idle exit as quickly as it could.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.11/cups-2.2.11-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.11-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.92 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.11/cups-2.2.11-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.11-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.11.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.11.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-prerelease">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">
        <span class="flex-shrink-0 Label Label--outline mb-md-2 mr-2 mr-md-0 Label--prerelease">
            Pre-release
        </span>


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.3b7" class="muted-link css-truncate" title="v2.3b7">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.3b7</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/b1e7afd5964cbe1aeab6f0a2d5b33884693a143a" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>b1e7afd</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.3b7">v2.3b7</a>
        </div>

          <span class="d-md-none flex-shrink-0 ml-2 mt-2 mt-md-0 Label Label--outline Label--prerelease">
              Pre-release
          </span>

      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.3b7" class="muted-link css-truncate" title="v2.3b7">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.3b7</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/b1e7afd5964cbe1aeab6f0a2d5b33884693a143a" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>b1e7afd</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2018-12-14T15:25:40Z" class="no-wrap">Dec 14, 2018</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.3b7...master">
    350 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.3b7 is now available for download.  This is the sixth beta of the CUPS 2.3 series which adopts the new CUPS license, adds support for IPP presets and finishing templates, and fixes a number of bugs and "polish" issues.</p>
<p>Changes include:</p>
<ul>
<li>Fixed some build failures (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="388917027" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5451" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5451/hovercard" href="https://github.com/apple/cups/pull/5451">#5451</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="391143584" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5463" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5463/hovercard" href="https://github.com/apple/cups/pull/5463">#5463</a>)</li>
<li>Running ppdmerge with the same input and output filenames did not work as<br>
advertised (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="389283806" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5455" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5455/hovercard" href="https://github.com/apple/cups/issues/5455">#5455</a>)</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3b7/cups-2.3b7-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3b7-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.77 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3b7/cups-2.3b7-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3b7-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3b7.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3b7.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-prerelease">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">
        <span class="flex-shrink-0 Label Label--outline mb-md-2 mr-2 mr-md-0 Label--prerelease">
            Pre-release
        </span>


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.3b6" class="muted-link css-truncate" title="v2.3b6">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.3b6</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/c13e2f44eb15bcdbff9126df86d1ed9a5108e47c" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>c13e2f4</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.3b6">v2.3b6</a>
        </div>

          <span class="d-md-none flex-shrink-0 ml-2 mt-2 mt-md-0 Label Label--outline Label--prerelease">
              Pre-release
          </span>

      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.3b6" class="muted-link css-truncate" title="v2.3b6">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.3b6</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/c13e2f44eb15bcdbff9126df86d1ed9a5108e47c" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>c13e2f4</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2018-12-07T19:55:15Z" class="no-wrap">Dec 7, 2018</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.3b6...master">
    358 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.3b6 is now available for download. This is the sixth beta of the CUPS 2.3 series which adopts the new CUPS license, adds support for IPP presets and finishing templates, and fixes a number of bugs and “polish” issues. Changes include:</p>
<ul>
<li>Localization update (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="331487448" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5339" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5339/hovercard" href="https://github.com/apple/cups/pull/5339">#5339</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="338371070" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5348" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5348/hovercard" href="https://github.com/apple/cups/pull/5348">#5348</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="346031038" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5362" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5362/hovercard" href="https://github.com/apple/cups/issues/5362">#5362</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="366787211" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5408" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5408/hovercard" href="https://github.com/apple/cups/pull/5408">#5408</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="368045074" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5410" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5410/hovercard" href="https://github.com/apple/cups/pull/5410">#5410</a>)</li>
<li>Documentation updates (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="348353282" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5369" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5369/hovercard" href="https://github.com/apple/cups/pull/5369">#5369</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="363404487" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5402" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5402/hovercard" href="https://github.com/apple/cups/issues/5402">#5402</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="363405764" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5403" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5403/hovercard" href="https://github.com/apple/cups/issues/5403">#5403</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="364552772" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5404" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5404/hovercard" href="https://github.com/apple/cups/issues/5404">#5404</a>)</li>
<li>CVE-2018-4700: Linux session cookies used a predictable random number seed.</li>
<li>All user commands now support the <code>--help</code> option (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="330540965" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5326" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5326/hovercard" href="https://github.com/apple/cups/pull/5326">#5326</a>)</li>
<li>The <code>lpoptions</code> command now works with IPP Everywhere printers that have not yet been added as local queues (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="240476360" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5045" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5045/hovercard" href="https://github.com/apple/cups/issues/5045">#5045</a>)</li>
<li>The lpadmin command would create a non-working printer in some error cases (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="318946590" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5305" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5305/hovercard" href="https://github.com/apple/cups/issues/5305">#5305</a>)</li>
<li>The scheduler would crash if an empty <code>AccessLog</code> directive was specified (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="320205820" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5309" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5309/hovercard" href="https://github.com/apple/cups/issues/5309">#5309</a>)</li>
<li>The scheduler did not idle-exit on some Linux distributions (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="326681481" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5319" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5319/hovercard" href="https://github.com/apple/cups/issues/5319">#5319</a>)</li>
<li>Fixed a regression in the changes to ippValidateAttribute (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="329849228" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5322" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5322/hovercard" href="https://github.com/apple/cups/issues/5322">#5322</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="330795910" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5330" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5330/hovercard" href="https://github.com/apple/cups/pull/5330">#5330</a>)</li>
<li>Fixed a crash bug in the Epson dot matrix driver (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="329933700" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5323" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5323/hovercard" href="https://github.com/apple/cups/issues/5323">#5323</a>)</li>
<li>Automatic debug logging of job errors did not work with systemd (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="331250505" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5337" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5337/hovercard" href="https://github.com/apple/cups/pull/5337">#5337</a>)</li>
<li>The web interface did not list the IPP Everywhere "driver" (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="331312712" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5338" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5338/hovercard" href="https://github.com/apple/cups/issues/5338">#5338</a>)</li>
<li>The scheduler did not report all of the supported job options and values (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="333276783" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5340" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5340/hovercard" href="https://github.com/apple/cups/issues/5340">#5340</a>)</li>
<li>The IPP Everywhere "driver" now properly supports face-up printers (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="337083449" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5345" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5345/hovercard" href="https://github.com/apple/cups/pull/5345">#5345</a>)</li>
<li>Fixed some typos in the label printer drivers (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="339185565" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5350" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5350/hovercard" href="https://github.com/apple/cups/pull/5350">#5350</a>)</li>
<li>Setting the <code>Community</code> name to the empty string in <code>snmp.conf</code> now disables SNMP supply level monitoring by all the standard network backends (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="340624090" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5354" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5354/hovercard" href="https://github.com/apple/cups/issues/5354">#5354</a>)</li>
<li>Multi-file jobs could get stuck if the backend failed (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="345069787" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5359" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5359/hovercard" href="https://github.com/apple/cups/issues/5359">#5359</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="371092957" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5413" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5413/hovercard" href="https://github.com/apple/cups/pull/5413">#5413</a>)</li>
<li>The IPP Everywhere "driver" no longer does local filtering when printing to a shared CUPS printer (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="345371064" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5361" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5361/hovercard" href="https://github.com/apple/cups/issues/5361">#5361</a>)</li>
<li>The lpadmin command now correctly reports IPP errors when configuring an IPP Everywhere printer (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="348454669" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5370" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5370/hovercard" href="https://github.com/apple/cups/issues/5370">#5370</a>)</li>
<li>Fixed some memory leaks discovered by Coverity (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="350817056" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5375" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5375/hovercard" href="https://github.com/apple/cups/pull/5375">#5375</a>)</li>
<li>The PPD compiler incorrectly terminated JCL options (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="353923453" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5379" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5379/hovercard" href="https://github.com/apple/cups/issues/5379">#5379</a>)</li>
<li>The cupstestppd utility did not generate errors for missing/mismatched CloseUI/JCLCloseUI keywords (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="354366018" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5381" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5381/hovercard" href="https://github.com/apple/cups/issues/5381">#5381</a>)</li>
<li>The scheduler now reports the actual location of the log file (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="360830362" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5398" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5398/hovercard" href="https://github.com/apple/cups/pull/5398">#5398</a>)</li>
<li>Added USB quirk rules (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="357733286" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5395" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5395/hovercard" href="https://github.com/apple/cups/issues/5395">#5395</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="374789312" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5420" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5420/hovercard" href="https://github.com/apple/cups/issues/5420">#5420</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="383353143" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5443" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5443/hovercard" href="https://github.com/apple/cups/issues/5443">#5443</a>)</li>
<li>The generated PPD files for IPP Everywhere printers did not contain the cupsManualCopies keyword (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="379168700" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5433" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5433/hovercard" href="https://github.com/apple/cups/issues/5433">#5433</a>)</li>
<li>Kerberos credentials might be truncated (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="379992572" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5435" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5435/hovercard" href="https://github.com/apple/cups/pull/5435">#5435</a>)</li>
<li>The handling of <code>MaxJobTime 0</code> did not match the documentation (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5438" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5438/hovercard" href="https://github.com/apple/cups/issues/5438">#5438</a>)</li>
<li>Fixed a bug adding a queue with the <code>-E</code> option (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5440" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5440/hovercard" href="https://github.com/apple/cups/issues/5440">#5440</a>)</li>
<li>The <code>cupsaddsmb</code> program has been removed (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="388302147" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5449" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5449/hovercard" href="https://github.com/apple/cups/issues/5449">#5449</a>)</li>
<li>The <code>cupstestdsc</code> program has been removed (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="388307516" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5450" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5450/hovercard" href="https://github.com/apple/cups/issues/5450">#5450</a>)</li>
<li>The scheduler was being backgrounded on macOS, causing applications to spin (rdar://40436080)</li>
<li>The scheduler did not validate that required initial request attributes were in the operation group (rdar://41098178)</li>
<li>Authentication in the web interface did not work on macOS (rdar://41444473)</li>
<li>Fixed an issue with HTTP Digest authentication (rdar://41709086)</li>
<li>The scheduler could crash when job history was purged (rdar://42198057)</li>
<li>Fixed a crash bug when mapping PPD duplex options to IPP attributes (rdar://********)</li>
<li>Fixed a memory leak for some IPP (extension) syntaxes.</li>
<li>The <code>cupscgi</code>, <code>cupsmime</code>, and <code>cupsppdc</code> support libraries are no longer installed as shared libraries.</li>
<li>The <code>snmp</code> backend is now deprecated.</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3b6/cups-2.3b6-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3b6-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.77 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.3b6/cups-2.3b6-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.3b6-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3b6.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.3b6.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>



        <div class="release-entry">
            
<div class="release pt-2 pt-md-0 pb-3 pb-md-0 clearfix label-">
  <div class="d-none d-md-block flex-wrap flex-items-center col-12 col-md-3 col-lg-2 px-md-3 pb-1 pb-md-4 pt-md-4 float-left text-md-right v-align-top">
    <div class="flex-auto flex-self-start">


    </div>

      <ul class="d-none d-md-block mt-2 list-style-none">
        <li class="d-block mb-1">
          <a href="/apple/cups/tree/v2.2.10" class="muted-link css-truncate" title="v2.2.10">
            <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
            <span class="css-truncate-target" style="max-width: 125px">v2.2.10</span>
          </a>
        </li>

        <li class="d-block mb-1">
          <a href="/apple/cups/commit/25b2338346ef3abbb93ea88476887cba7b2b86f8" class="muted-link">
            <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
            <code>25b2338</code>
          </a>
        </li>
        
      </ul>
      

  </div><!-- /.meta -->

  <div class="col-12 col-md-9 col-lg-10 px-md-3 py-md-4 release-main-section commit open float-left">
    <div class="release-header">
      <div class="d-flex flex-items-start">
        <div class="f1 flex-auto min-width-0 text-normal">
          <a href="/apple/cups/releases/tag/v2.2.10">v2.2.10</a>
        </div>


      </div>
        <ul class="d-flex d-md-none flex-items-center mb-1 list-style-none">
          <li class="d-block mr-2">
            <a href="/apple/cups/tree/v2.2.10" class="muted-link css-truncate" title="v2.2.10">
              <svg class="octicon octicon-tag" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.73 1.73C7.26 1.26 6.62 1 5.96 1H3.5C2.13 1 1 2.13 1 3.5v2.47c0 .66.27 1.3.73 1.77l6.06 6.06c.39.39 1.02.39 1.41 0l4.59-4.59a.996.996 0 000-1.41L7.73 1.73zM2.38 7.09c-.31-.3-.47-.7-.47-1.13V3.5c0-.88.72-1.59 1.59-1.59h2.47c.42 0 .83.16 1.13.47l6.14 6.13-4.73 4.73-6.13-6.15zM3.01 3h2v2H3V3h.01z"/></svg>
              <span class="css-truncate-target" style="max-width: 125px">v2.2.10</span>
            </a>
          </li>

          <li class="d-block mr-2 flex-auto">
            <a href="/apple/cups/commit/25b2338346ef3abbb93ea88476887cba7b2b86f8" class="muted-link">
              <svg class="octicon octicon-git-commit" viewBox="0 0 14 16" version="1.1" width="14" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M10.86 7c-.45-1.72-2-3-3.86-3-1.86 0-3.41 1.28-3.86 3H0v2h3.14c.45 1.72 2 3 3.86 3 1.86 0 3.41-1.28 3.86-3H14V7h-3.14zM7 10.2c-1.22 0-2.2-.98-2.2-2.2 0-1.22.98-2.2 2.2-2.2 1.22 0 2.2.98 2.2 2.2 0 1.22-.98 2.2-2.2 2.2z"/></svg>
              <code>25b2338</code>
            </a>
          </li>
          

          

        </ul>
      
<p class="f5 text-gray mt-2 mt-md-1 mb-2 mb-md-4">
    <a class="d-inline-block" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet"><img class="avatar" src="https://avatars3.githubusercontent.com/u/488103?s=40&amp;v=4" width="20" height="20" alt="@michaelrsweet" /></a>
    <a class="text-bold text-gray" data-hovercard-type="user" data-hovercard-url="/users/michaelrsweet/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/michaelrsweet">michaelrsweet</a>
    released this
    <relative-time datetime="2018-12-07T19:57:55Z" class="no-wrap">Dec 7, 2018</relative-time>
    &middot;
    <a href="/apple/cups/compare/v2.2.10...master">
    793 commits</a>
    to master
    since this release
</p>
    </div>

    

  <div class="markdown-body">
    <p>CUPS 2.2.10 is a bug fix release that addresses issues in the scheduler, IPP Everywhere support, CUPS library, and USB printer support. Changes include:</p>
<ul>
<li>CVE-2018-4300: Linux session cookies used a predictable random number seed.</li>
<li>The <code>lpoptions</code> command now works with IPP Everywhere printers that have not yet been added as local queues (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="240476360" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5045" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5045/hovercard" href="https://github.com/apple/cups/issues/5045">#5045</a>)</li>
<li>Added USB quirk rules (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="357733286" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5395" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5395/hovercard" href="https://github.com/apple/cups/issues/5395">#5395</a>, Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="383353143" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5443" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5443/hovercard" href="https://github.com/apple/cups/issues/5443">#5443</a>)</li>
<li>The generated PPD files for IPP Everywhere printers did not contain the cupsManualCopies keyword (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="379168700" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5433" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5433/hovercard" href="https://github.com/apple/cups/issues/5433">#5433</a>)</li>
<li>Kerberos credentials might be truncated (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="379992572" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5435" data-hovercard-type="pull_request" data-hovercard-url="/apple/cups/pull/5435/hovercard" href="https://github.com/apple/cups/pull/5435">#5435</a>)</li>
<li>The handling of <code>MaxJobTime 0</code> did not match the documentation (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5438" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5438/hovercard" href="https://github.com/apple/cups/issues/5438">#5438</a>)</li>
<li>Incorporated the page accounting changes from CUPS 2.3 (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5439" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5439/hovercard" href="https://github.com/apple/cups/issues/5439">#5439</a>)</li>
<li>Fixed a bug adding a queue with the <code>-E</code> option (Issue <a class="issue-link js-issue-link" data-error-text="Failed to load issue title" data-id="*********" data-permission-text="Issue title is private" data-url="https://github.com/apple/cups/issues/5440" data-hovercard-type="issue" data-hovercard-url="/apple/cups/issues/5440/hovercard" href="https://github.com/apple/cups/issues/5440">#5440</a>)</li>
<li>Fixed a crash bug when mapping PPD duplex options to IPP attributes (rdar://********)</li>
</ul>
<p>Enjoy!</p>
  </div>


  <details
    class="details-reset Details-element border-top pt-3 mt-4 mb-2 mb-md-4"
    
    
    >
    <summary>
      <div class="d-flex flex-items-center">
        <span class="mr-2 Details-content--closed"><svg class="octicon octicon-triangle-right" viewBox="0 0 6 16" version="1.1" width="6" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 14l6-6-6-6v12z"/></svg></span>
        <span class="mr-2 Details-content--open"><svg class="octicon octicon-triangle-down" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 5l6 6 6-6H0z"/></svg></span>
        <span class="text-bold">Assets</span>
        <span class="ml-1 Counter">4</span>
      </div>
    </summary>
    <div class="Box Box--condensed mt-3">
      <div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.10/cups-2.2.10-source.tar.gz" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.10-source.tar.gz</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">9.92 MB</small>
          </div>
          <div class="d-flex flex-justify-between flex-items-center py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/releases/download/v2.2.10/cups-2.2.10-source.tar.gz.sig" rel="nofollow" class="d-flex flex-items-center min-width-0">
              <svg class="octicon octicon-package flex-shrink-0 text-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1 4.27v7.47c0 .45.3.84.75.97l6.5 1.73c.16.05.34.05.5 0l6.5-1.73c.45-.13.75-.52.75-.97V4.27c0-.45-.3-.84-.75-.97l-6.5-1.74a1.4 1.4 0 00-.5 0L1.75 3.3c-.45.13-.75.52-.75.97zm7 9.09l-6-1.59V5l6 1.61v6.75zM2 4l2.5-.67L11 5.06l-2.5.67L2 4zm13 7.77l-6 1.59V6.61l2-.55V8.5l2-.53V5.53L15 5v6.77zm-2-7.24L6.5 2.8l2-.53L15 4l-2 .53z"/></svg>
              <span class="pl-2 flex-auto min-width-0 text-bold">cups-2.2.10-source.tar.gz.sig</span>
            </a>
            <small class="pl-2 text-gray flex-shrink-0">585 Bytes</small>
          </div>


          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.10.zip" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (zip)
            </a>
          </div>
          <div class="d-block py-1 py-md-2 Box-body px-2">
            <a href="/apple/cups/archive/v2.2.10.tar.gz" rel="nofollow" class="d-flex flex-items-center">
              <svg class="octicon octicon-file-zip flex-shrink-0 text-gray" width="16" height="16" viewBox="0 0 12 16" version="1.1" aria-hidden="true"><path fill-rule="evenodd" d="M8.5 1H1a1 1 0 00-1 1v12a1 1 0 001 1h10a1 1 0 001-1V4.5L8.5 1zM11 14H1V2h3v1h1V2h3l3 3v9zM5 4V3h1v1H5zM4 4h1v1H4V4zm1 2V5h1v1H5zM4 6h1v1H4V6zm1 2V7h1v1H5zM4 9.28A2 2 0 003 11v1h4v-1a2 2 0 00-2-2V8H4v1.28zM6 10v1H4v-1h2z"/></svg>
              <span class="px-1 text-bold">Source code</span> (tar.gz)
            </a>
          </div>
      </div>
    </div>
  </details>


  </div><!-- /.release-body -->
</div><!-- /.release -->

        </div>

  </div>

  <div data-pjax class="paginate-container">
    <div class="pagination"><span class="disabled">Previous</span><a rel="nofollow" href="https://github.com/apple/cups/releases?after=v2.2.10">Next</a></div>
  </div>


  </div>
</div>

    </main>
  </div>
  

  </div>

        
<div class="footer container-lg width-full p-responsive" role="contentinfo">
  <div class="position-relative d-flex flex-row-reverse flex-lg-row flex-wrap flex-lg-nowrap flex-justify-center flex-lg-justify-between pt-6 pb-2 mt-6 f6 text-gray border-top border-gray-light ">
    <ul class="list-style-none d-flex flex-wrap col-12 col-lg-5 flex-justify-center flex-lg-justify-between mb-2 mb-lg-0">
      <li class="mr-3 mr-lg-0">&copy; 2019 GitHub, Inc.</li>
        <li class="mr-3 mr-lg-0"><a data-ga-click="Footer, go to terms, text:terms" href="https://github.com/site/terms">Terms</a></li>
        <li class="mr-3 mr-lg-0"><a data-ga-click="Footer, go to privacy, text:privacy" href="https://github.com/site/privacy">Privacy</a></li>
        <li class="mr-3 mr-lg-0"><a data-ga-click="Footer, go to security, text:security" href="https://github.com/security">Security</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://githubstatus.com/" data-ga-click="Footer, go to status, text:status">Status</a></li>
        <li><a data-ga-click="Footer, go to help, text:help" href="https://help.github.com">Help</a></li>
    </ul>

    <a aria-label="Homepage" title="GitHub" class="footer-octicon d-none d-lg-block mx-lg-4" href="https://github.com">
      <svg height="24" class="octicon octicon-mark-github" viewBox="0 0 16 16" version="1.1" width="24" aria-hidden="true"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"/></svg>
</a>
   <ul class="list-style-none d-flex flex-wrap col-12 col-lg-5 flex-justify-center flex-lg-justify-between mb-2 mb-lg-0">
        <li class="mr-3 mr-lg-0"><a data-ga-click="Footer, go to contact, text:contact" href="https://github.com/contact">Contact GitHub</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://github.com/pricing" data-ga-click="Footer, go to Pricing, text:Pricing">Pricing</a></li>
      <li class="mr-3 mr-lg-0"><a href="https://developer.github.com" data-ga-click="Footer, go to api, text:api">API</a></li>
      <li class="mr-3 mr-lg-0"><a href="https://training.github.com" data-ga-click="Footer, go to training, text:training">Training</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://github.blog" data-ga-click="Footer, go to blog, text:blog">Blog</a></li>
        <li><a data-ga-click="Footer, go to about, text:about" href="https://github.com/about">About</a></li>

    </ul>
  </div>
  <div class="d-flex flex-justify-center pb-6">
    <span class="f6 text-gray-light"></span>
  </div>
</div>



  <div id="ajax-error-message" class="ajax-error-message flash flash-error">
    <svg class="octicon octicon-alert" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 000 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 00.01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"/></svg>
    <button type="button" class="flash-close js-ajax-error-dismiss" aria-label="Dismiss error">
      <svg class="octicon octicon-x" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48L7.48 8z"/></svg>
    </button>
    You can’t perform that action at this time.
  </div>


    <script crossorigin="anonymous" integrity="sha512-mdrBasYG+QjgS391PSyVkPr06io3gWplCVbPscguetNEHxIEt+mZwCeCxPR9eMNfda6qNuibNFqBo5ak2+O/hg==" type="application/javascript" src="https://github.githubassets.com/assets/compat-bootstrap-99dac16a.js"></script>
    <script crossorigin="anonymous" integrity="sha512-QxcIZ5pa4j8P5eL6FLQaU/FAIfodWkzV2rf+7OlyiiiIM9ZaG3MODvgQme+LVDUPMBdmjYYZ+svSlFu5yjFhRw==" type="application/javascript" src="https://github.githubassets.com/assets/frameworks-43170867.js"></script>
    
    <script crossorigin="anonymous" async="async" integrity="sha512-pWb+XERZ8B74prW8yPNeOMfFEfMfo8DvbO4kDA/DzPk10uGiVYPCB51sRRwisgKrPyfXtC+Igc5k9BfUpepmtQ==" type="application/javascript" src="https://github.githubassets.com/assets/github-bootstrap-a566fe5c.js"></script>
    
    
    
  <div class="js-stale-session-flash flash flash-warn flash-banner" hidden
    >
    <svg class="octicon octicon-alert" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 000 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 00.01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"/></svg>
    <span class="js-stale-session-flash-signed-in" hidden>You signed in with another tab or window. <a href="">Reload</a> to refresh your session.</span>
    <span class="js-stale-session-flash-signed-out" hidden>You signed out in another tab or window. <a href="">Reload</a> to refresh your session.</span>
  </div>
  <template id="site-details-dialog">
  <details class="details-reset details-overlay details-overlay-dark lh-default text-gray-dark hx_rsm" open>
    <summary role="button" aria-label="Close dialog"></summary>
    <details-dialog class="Box Box--overlay d-flex flex-column anim-fade-in fast hx_rsm-dialog hx_rsm-modal">
      <button class="Box-btn-octicon m-0 btn-octicon position-absolute right-0 top-0" type="button" aria-label="Close dialog" data-close-dialog>
        <svg class="octicon octicon-x" viewBox="0 0 12 16" version="1.1" width="12" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48L7.48 8z"/></svg>
      </button>
      <div class="octocat-spinner my-6 js-details-dialog-spinner"></div>
    </details-dialog>
  </details>
</template>

  <div class="Popover js-hovercard-content position-absolute" style="display: none; outline: none;" tabindex="0">
  <div class="Popover-message Popover-message--bottom-left Popover-message--large Box box-shadow-large" style="width:360px;">
  </div>
</div>

  <div aria-live="polite" class="js-global-screen-reader-notice sr-only"></div>

  </body>
</html>

