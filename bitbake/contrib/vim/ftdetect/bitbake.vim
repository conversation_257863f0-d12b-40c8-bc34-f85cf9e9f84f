" Vim filetype detection file
" Language:     BitBake
" Author:       <PERSON> <<EMAIL>>
" Copyright:    Copyright (C) 2008  <PERSON> <<EMAIL>>
" Licence:      You may redistribute this under the same terms as Vim itself
"
" This sets up the syntax highlighting for BitBake files, like .bb, .bbclass and .inc

if &compatible || version < 600 || exists("b:loaded_bitbake_plugin")
    finish
endif

" .bb, .bbappend and .bbclass
au BufNewFile,BufRead *.{bb,bbappend,bbclass}  set filetype=bitbake

" .inc
au BufNewFile,BufRead *.inc		set filetype=bitbake

" .conf
au BufNewFile,BufRead *.conf
    \ if (match(expand("%:p:h"), "conf") > 0) |
    \     set filetype=bitbake |
    \ endif

